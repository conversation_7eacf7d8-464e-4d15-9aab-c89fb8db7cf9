package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
)

type RoleMgrListReq struct {
	g.Meta `path:"/roleMgr/list" method:"post" tags:"角色管理V2" summary:"列表"   `
	ListReq
	Name string `json:"name" dc:"角色名称"`
}
type RoleMgrListRes struct {
	ListRes
	List []*RoleConfig `json:"list"`
}

type RoleConfig struct {
	Id             uint             `json:"id"            dc:""`
	Name           string           `json:"name"          dc:"角色名称"`
	RoleLevel      int              `json:"roleLevel"     dc:"角色层级：1管理员 2站长 3站员"`
	Label          string           `json:"label"         dc:"角色名称（中）"`
	PermissionSet  []uint           `json:"permissionSet" dc:"权限id数组"`
	MarkedFieldCfg PermID2MaskedCfg `json:"markedFieldCfg"  dc:"掩码字段配置"`
	MaskedFields   string           `json:"maskedFields"  dc:"权限ID对应的掩码字段集合"`
	Remark         string           `json:"remark"        dc:"备注"`
	OrderBy        int              `json:"orderBy"       dc:"排序"`
}

type PermissionFieldConfig struct {
	NodePath     string   `json:"nodePath"          dc:"节点路径：node1.node2"`
	MaskedFields []string `json:"maskedFields"  dc:"掩码字段"`
}

type RoleMgrAddReq struct {
	g.Meta      `path:"/roleMgr/add" method:"post" tags:"角色管理V2" summary:"新增"`
	Name        string               `v:"required" json:"name" dc:"角色编号"`
	RoleLevel   int                  `json:"roleLevel"     dc:"角色层级：1管理员 2站长 3站员"`
	Remark      string               `json:"remark" dc:"角色描述"`
	OrderBy     int                  `json:"orderBy"       dc:"排序"`
	Permissions []*RolePermissionCfg `v:"required" json:"permissions" dc:"权限 脱敏配置"`
}

type Name2FieldPath map[string]string
type PermID2MaskedCfg map[uint]Name2FieldPath

type RolePermissionCfg struct {
	PermissionId     uint           `v:"required" json:"permissionId" dc:"权限id"`
	MapNameFieldPath Name2FieldPath `json:"mapNameFieldPath" dc:"脱敏字段配置"`
}

type RoleMgrEditReq struct {
	g.Meta      `path:"/roleMgr/edit" method:"post" tags:"角色管理V2" summary:"编辑"`
	Id          uint                 `v:"required|min:0" json:"id" dc:"角色id"`
	Name        string               `json:"name" dc:"角色编号"`
	RoleLevel   int                  `json:"roleLevel"     dc:"角色层级：1管理员 2站长 3站员"`
	Remark      *string              `json:"remark" dc:"角色描述"`
	OrderBy     int                  `json:"orderBy"       dc:"排序"`
	Permissions []*RolePermissionCfg `v:"required" json:"permissions" dc:"权限 脱敏配置"`
}

type RoleMgrDeleteReq struct {
	g.Meta `path:"/roleMgr/delete" method:"post" tags:"角色管理V2" summary:"删除"`
	Id     uint `v:"required|min:0" json:"id" dc:"角色id"`
}

type RoleMgrDetailReq struct {
	g.Meta `path:"/roleMgr/detail" method:"post" tags:"角色管理V2" summary:"详情"`
	Id     uint `v:"required" json:"id" dc:"角色id"`
}
type RoleMgrDetailRes struct {
	RoleConfig
	PermissionNames []string `json:"permissionNames" dc:"权限名称"`
}

type RoleMgrOptionsReq struct {
	g.Meta `path:"/roleMgr/options" method:"post" tags:"角色管理V2" summary:"选择器"`
}
type RoleMgrOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}
