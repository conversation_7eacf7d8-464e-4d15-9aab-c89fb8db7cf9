package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model/entity"
)

type LoginInReq struct {
	g.Meta    `path:"/loginMgr/signIn" method:"post" tags:"登录管理" summary:"登入"`
	Account   string `v:"required" json:"account"`  // |passport
	Password  string `v:"required" json:"password"` // |password3
	Key       string `v:"required" json:"key" dc:"验证码标识"`
	Captcha   string `v:"required" json:"captcha" dc:"验证码 仅能验证一次,失败后 前端请刷新验证码"`
	GoogleOtp int32  `json:"googleOtp" dc:"谷歌验证码"`
}
type LoginInRes struct {
	Token string `json:"token"`
}

type LoginOutReq struct {
	g.Meta `path:"/loginMgr/signOut" method:"post" tags:"登录管理" summary:"登出"`
}

type LoginLogReq struct {
	g.Meta `path:"/loginMgr/listLog" method:"post" tags:"登录管理" summary:"登录日志"`
	ListReq
	Account   string `json:"account" dc:"登录用户"`
	Ip        string `json:"ip" dc:"登录IP"`
	LoginType int    `json:"loginType" dc:"登录类型 1 登入 2 登出 3 修改密码"`
	StartTime int64  `json:"startTime" dc:"开始时间"`
	EndTime   int64  `json:"endTime" dc:"结束时间"`
}

type LoginLogRes struct {
	ListRes
	List []*entity.AccountLoginLog `json:"list"`
}

type ModifyPasswordReq struct {
	g.Meta      `path:"/loginMgr/modifyPassword" method:"post" tags:"管理员操作" summary:"修改密码"`
	OldPassword string `v:"required" json:"oldPassword" dc:"老密码"`
	NewPassword string `v:"required" json:"newPassword" dc:"修改后密码"`
}
