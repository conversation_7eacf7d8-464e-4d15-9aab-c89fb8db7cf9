package v1

import (
	"github.com/gogf/gf/v2/container/gvar"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
	"strings"
)

// 认证
type Author struct {
	Authorization string `p:"Authorization" in:"header" dc:"Bearer {{token}}"`
}

// EmptyRes 不响应任何数据
type EmptyRes struct {
	g.Meta `mime:"application/json"`
}

// Data没有任何数据
type EmptyDataRes struct {
}

// 批量更新结果
type BatchUpdateRes struct {
	NumSuccess int64 `json:"numSuccess" dc:"成功条数"`
	NumFail    int64 `json:"numFail" dc:"失败条数"`
}

type ListReq struct {
	// Current  int    `v:"required|integer|gte:1" json:"current" d:"1" dc:"当前页码"`
	Current  int    `v:"required|integer" json:"current" d:"1" dc:"当前页码"`
	PageSize int    `v:"required|integer|between:1,10000" json:"pageSize" d:"15" dc:"每页数量"` // 每页数
	OrderBy  string `json:"orderBy" dc:"参考格式 \"order_by desc,id desc\""`                    // 排序方式
	Offset   int    `v:"integer " json:"Offset" dc:"偏移量 解决深分页"`
}

func (lr ListReq) GetListInput() model.ListInput {
	return model.ListInput{
		Current:  lr.Current,
		PageSize: lr.PageSize,
		OrderBy:  lr.OrderBy,
	}
}

// 解析 OrderBy 获取字段名 和 排序方式：升序或降序
func (s *ListReq) SplitOrder() (field string, isAsc bool, err error) {
	if len(s.OrderBy) == 0 {
		return "", false, gerror.New("empty")
	}

	vals := strings.Split(s.OrderBy, " ")
	if len(vals) != 2 {
		return "", false, gerror.New("format err")
	}

	field = vals[0]
	lowStr := strings.ToLower(vals[1])
	if !strings.Contains(lowStr, "desc") {
		isAsc = true
	}
	return
}

// ListRes 列表公共返回
type ListRes struct {
	Current int `json:"current" dc:"当前页码"`
	Total   int `json:"total" dc:"总条数"`
	Offset  int `json:"offset" dc:"当前数据最大的偏移量 下次分页传过来"`
}

func (lr *ListRes) Set(output model.ListOutput) {
	lr.Total = output.Total
}

// 前端信息采集
type FrontInfo struct {
	DeviceId        string `v:"required" json:"deviceId" dc:"设备号（设备指纹）"`
	DeviceOs        string `v:"required" json:"deviceOs" dc:"设备操作系统（android,ios,windows,mac,...）"`
	DeviceOsVersion string `json:"deviceOsVersion" dc:"设备操作系统版本号"`
	DeviceType      int    `v:"required|in:1,2,3,4" json:"deviceType" dc:"设备类型（1:mobile手机,2:desktop台式,3:pad平板，4:其他）"`
	AppType         int    `v:"required|in:1,2,3,4,5" json:"appType" dc:"应用类型（1:android  2: ios，3:h5，4:web，5:其他 ）"`
	AppVersion      string `v:"required" json:"appVersion" dc:"应用版本号"`
}

// AddRes 新添记录返回id
type AddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

// ChartLine 数据图的天数据项
type ChartLine struct {
	Date      string           `json:"date" dc:"日期天 比如 2024-1-16"`
	HourItems []*ChartHourItem `json:"hourItems"  dc:"该天对应的小时值"`
}

// ChartHourItem 数据图的小时 数据项
type ChartHourItem struct {
	Hour  string    `json:"hour" dc:"小时整点值"`
	Value *gvar.Var `json:"value"  dc:"小时整点时刻对应的指标数值"`
}
