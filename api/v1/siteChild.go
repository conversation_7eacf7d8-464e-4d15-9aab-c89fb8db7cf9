package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"gtcms/internal/model/entity"
)

type SiteChildListReq struct {
	g.Meta   `path:"/siteChild/list" tags:"站点子站管理" method:"post" summary:"子站点列表"`
	MotherId uint `v:"required" json:"motherId" dc:"母站id集合"`
	ListReq
}

type SiteChildListRes struct {
	ListRes
	List []SiteChildItem `json:"list" dc:"列表"`
}

type SiteChildItem struct {
	entity.SiteChild
	TmplName string `json:"tmplName"          dc:"模版名称"`
	TdkName  string `json:"tdkName"          dc:"tdk名称"`
	UrlName  string `json:"urlName"          dc:"url名称"`
	Domain   string `json:"domain" dc:"域名"`
}

type SiteChildAddReq struct {
	g.Meta            `path:"/siteChild/add" tags:"站点子站管理" method:"post" summary:"添加子站点"`
	MotherId          uint   `v:"required" json:"motherId" dc:"母站id"`
	Name              string `v:"required" json:"name"              dc:"名称"`
	Language          string `v:"required" json:"language"          dc:"语言"`
	TmplId            uint   `v:"required" json:"tmplId"            dc:"模版id"`
	Remark            string `json:"remark"            dc:"备注"`
	Creater           string `json:"creater"           dc:"创建者"`
	Icp               string `json:"icp"               dc:"备案号"`
	Picp              string `json:"picp"              dc:"公安备案号"`
	SeoTitle          string `v:"required" json:"seoTitle"          dc:"首页title"`
	SeoKeyword        string `v:"required" json:"seoKeyword"       dc:"首页关键词"`
	SeoDesc           string `v:"required" json:"seoDesc"            dc:"首页描述"`
	StatisticsCode    string `json:"statisticsCode"    dc:"统计代码"`
	BaiduPush         string `json:"baiduPush"         dc:"百度推送"`
	ShenmaPushAccount string `json:"shenmaPushAccount" dc:"神马推送-站长平台账号"`
	ShenmaPushAuthkey string `json:"shenmaPushAuthkey" dc:"神马推送-域名authkey"`
	ToutiaoPush       string `json:"toutiaoPush"       dc:"今日头条推送"`
	Logo              string `json:"logo"              dc:"站点logo"`
	Favicon           string `json:"favicon"           dc:"网站图标"`
	TdkId             uint   `v:"required" json:"tdkId"           dc:"tdkId"`
	UrlId             uint   `v:"required" json:"urlId"           dc:"urlId"`
	BaiduVerifyCode   string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode  string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`
	GroupId           uint   `json:"groupId"           dc:"组id"`
}

type SiteChildAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type SiteChildEditReq struct {
	g.Meta            `path:"/siteChild/edit" tags:"站点子站管理" method:"post" summary:"编辑子站点"`
	Id                uint    `v:"required" json:"id"         dc:"id"`
	Name              *string `json:"name"              dc:"名称"`
	Language          *string `json:"language"          dc:"语言"`
	TmplId            *uint   `json:"tmplId"            dc:"模版id"`
	Remark            *string `json:"remark"            dc:"备注"`
	Icp               *string `json:"icp"               dc:"备案号"`
	Picp              *string `json:"picp"              dc:"公安备案号"`
	SeoTitle          *string `json:"seoTitle"          dc:"首页title"`
	SeoKeyword        *string `json:"seoKeyword"       dc:"首页关键词"`
	SeoDesc           *string `json:"seoDesc"            dc:"首页描述"`
	StatisticsCode    *string `json:"statisticsCode"    dc:"统计代码"`
	BaiduPush         *string `json:"baiduPush"         dc:"百度推送"`
	ShenmaPushAccount *string `json:"shenmaPushAccount" dc:"神马推送-站长平台账号"`
	ShenmaPushAuthkey *string `json:"shenmaPushAuthkey" dc:"神马推送-域名authkey"`
	ToutiaoPush       *string `json:"toutiaoPush"       dc:"今日头条推送"`
	Logo              *string `json:"logo"              dc:"站点logo"`
	Favicon           *string `json:"favicon"           dc:"网站图标"`
	TdkId             *int    `json:"tdkId"           dc:"tdkId"`
	UrlId             *int    `json:"urlId"           dc:"urlId"`
	BaiduVerifyCode   *string `json:"baiduVerifyCode" dc:"百度站长平台验证码"`
	GoogleVerifyCode  *string `json:"googleVerifyCode" dc:"谷歌站长平台验证码"`
	AutoPublishNews   *int    `json:"autoPublishNews" dc:"是否自动发布采集到的新闻(1:是 2:否)"`
	KeyUpdatesNews    *int    `json:"keyUpdatesNews" dc:"是否重点更新新闻(1是 2否)"`
	GroupId           *uint   `json:"groupId"           dc:"组id"`
}

type SiteChildDeleteReq struct {
	g.Meta `path:"/siteChild/delete" tags:"站点子站管理" method:"post" summary:"删除子站点/批量删除"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type SiteChildOneReq struct {
	g.Meta `path:"/siteChild/one" method:"post" tags:"站点子站管理" summary:"子站点->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type SiteChildOneRes struct {
	SiteChildItem
}

type SiteChildEditBatchReq struct {
	g.Meta         `path:"/siteChild/editBatch" tags:"站点子站管理" method:"post" summary:"批量更新"`
	Ids            []uint  `v:"required" json:"ids"        dc:"id"`
	GroupId        *uint   `json:"groupId"           dc:"组id"`
	TmplId         *uint   `json:"tmplId"            dc:"模版id"`
	StatisticsCode *string `json:"statisticsCode"    dc:"统计代码"`
	TdkId          *int    `json:"tdkId"           dc:"tdkId"`
	UrlId          *int    `json:"urlId"           dc:"urlId"`
	Language       *string `json:"language"          dc:"语言"`
}

type SiteChildImportReq struct {
	g.Meta `path:"/siteChild/import" method:"post" mime:"multipart/form-data" tags:"站点子站管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}
