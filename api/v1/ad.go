package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
	"gtcms/internal/model/entity"
)

type AdListReq struct {
	g.Meta   `path:"/ad/list" tags:"广告管理" method:"post" summary:"广告列表"`
	Name     *string `json:"name" dc:"广告名称"`
	Type     *int    `json:"type" dc:"类型(1:文字 2:图片 3:视频)"`
	Belong   int     `json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId *uint   `json:"belongId" dc:"所属功能id"`
	Language *string `json:"language" dc:"语言"`
	ListReq
}

type AdListRes struct {
	ListRes
	List []AdItem `json:"list" dc:"列表"`
}

type AdItem struct {
	entity.Ad
	UrlName      string `json:"urlName"     dc:"链接名称"`
	ChooseStatus int    `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
	GroupName    string `json:"groupName"     dc:"分组名称"`
	Language     string `json:"language" dc:"语言"`
}

type AdAddReq struct {
	g.Meta      `path:"/ad/add" tags:"广告管理" method:"post" summary:"添加广告"`
	Type        int    `v:"required|in:1,2,3" json:"type"       dc:"类型(1:文字 2:图片 3:视频)"`
	Name        string `v:"required" json:"name"       dc:"名称"`
	Url         int    `v:"required" json:"url"        dc:"链接"`
	Content     string `v:"required-if:type,1" json:"content"    dc:"内容"`
	ExpireTime  int64  `v:"required" json:"expireTime" dc:"到期时间"`
	Image       string `json:"image"      dc:"广告图片-pc端"`
	ImageMobile string `json:"imageMobile"      dc:"广告图片-手机端"`
	Remark      string `json:"remark"     dc:"备注"`
	Belong      int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId    *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	ShowPc      int    `json:"showPc"     dc:"是否显示pc端(1:是 2:否)"`
	ShowMobile  int    `json:"showMobile" dc:"是否显示移动端(1:是 2:否)"`
	OpenReferer int    `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
}

type AdAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type AdEditReq struct {
	g.Meta      `path:"/ad/edit" tags:"广告管理" method:"post" summary:"编辑广告"`
	Id          uint    `v:"required" json:"id"         dc:"id"`
	Type        *int    `json:"type"       dc:"类型(1:文字 2:图片 3:视频)"`
	Name        *string `json:"name"       dc:"名称"`
	Url         *int    `json:"url"        dc:"链接"`
	Content     *string `json:"content"    dc:"内容"`
	ExpireTime  *int64  `json:"expireTime" dc:"到期时间"`
	Size        *string `json:"size"       dc:"尺寸"`
	Image       *string `json:"image"      dc:"广告图片-pc端"`
	ImageMobile *string `json:"imageMobile"      dc:"广告图片-手机端"`
	Remark      *string `json:"remark"     dc:"备注"`
	Belong      int     `v:"required" json:"belong" dc:"所属功能(0:广告 1:分组广告 2:站点广告)"`
	ShowPc      *int    `json:"showPc"     dc:"是否显示pc端(1:是 2:否)"`
	ShowMobile  *int    `json:"showMobile" dc:"是否显示移动端(1:是 2:否)"`
	OpenReferer *int    `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
}

type AdDeleteReq struct {
	g.Meta `path:"/ad/delete" tags:"广告管理" method:"post" summary:"删除广告"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
	Belong int    `v:"required" json:"belong" dc:"所属功能(0:广告 1:分组广告 2:站点广告)" d:"0"`
}

type AdOneReq struct {
	g.Meta `path:"/ad/one" method:"post" tags:"广告管理" summary:"广告->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type AdOneRes struct {
	AdItem
}

type AdGroupSiteAllListReq struct {
	g.Meta   `path:"/ad/groupSiteAllList" tags:"广告管理" method:"post" summary:"分组/站点广告列表(全部)"`
	Name     *string `json:"name" dc:"广告名称"`
	Type     *int    `json:"type" dc:"类型(1:文字 2:图片 3:视频)"`
	Belong   int     `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint   `v:"required" json:"belongId" dc:"所属功能id"`
	Status   *int    `json:"status" dc:"状态(1:显示 2:隐藏)"`
	ListReq
}

type AdGroupSiteAllListRes struct {
	ListRes
	List []AdItem `json:"list" dc:"列表"`
}

type AdGroupSiteAddReq struct {
	g.Meta `path:"/ad/groupSiteAdd" tags:"广告管理" method:"post" summary:"分组/站点广告添加"`
	model.GroupSiteAddReqIn
}

type AdAddBatchReq struct {
	g.Meta      `path:"/ad/addBatch" tags:"广告管理" method:"post" summary:"批量新增"`
	GroupIds    []uint `json:"groupIds"         dc:"分组id"`
	Type        int    `v:"required|in:1,2,3" json:"type"       dc:"类型(1:文字 2:图片 3:视频)"`
	Name        string `v:"required" json:"name"       dc:"名称"`
	Url         int    `v:"required" json:"url"        dc:"链接"`
	Content     string `v:"required-if:type,1" json:"content"    dc:"内容"`
	ExpireTime  int64  `v:"required" json:"expireTime" dc:"到期时间"`
	Image       string `json:"image"      dc:"广告图片-pc端"`
	ImageMobile string `json:"imageMobile"      dc:"广告图片-手机端"`
	Remark      string `json:"remark"     dc:"备注"`
	ShowPc      int    `json:"showPc"     dc:"是否显示pc端(1:是 2:否)"`
	ShowMobile  int    `json:"showMobile" dc:"是否显示移动端(1:是 2:否)"`
	OpenReferer int    `json:"openReferer" dc:"是否开启搜索来路校验(1:是 2:否)"`
}

type NginxReq struct {
	g.Meta      `path:"/nginx/records" tags:"nginx访问量" method:"post" summary:"nginx访问量"`
	InputMinute int `json:"inputMinute"    dc:"输入分钟数"`
}
type NginxWebsReq struct {
	g.Meta `path:"/nginx/websites" tags:"websites" method:"get" summary:"websites"`
}
type NginxOverallReq struct {
	g.Meta    `path:"/nginx/stats/overall" tags:"websites" method:"get" summary:"overall"`
	Id        string `json:"inputMinute"    dc:"websites id"`
	TimeRange string `json:"timeRange"    dc:"timeRange"`
}
type NginxSeriesReq struct {
	g.Meta    `path:"/nginx/stats/timeseries" tags:"websites" method:"get" summary:"timeseries"`
	Id        string `json:"inputMinute"    dc:"websites id"`
	TimeRange string `json:"timeRange"    dc:"timeRange"`
	ViewType  string `json:"viewType"    dc:"viewType"`
}

type NginxRes struct {
	Records int `json:"records" dc:"nginx记录数"`
}

type NginxWebsRes struct {
	Websites []struct {
		Id   string `json:"id"`
		Name string `json:"name"`
	} `json:"websites"`
}
type NginxOverallRes struct {
	Pv      int `json:"pv"`
	Uv      int `json:"uv"`
	Traffic int `json:"traffic"`
}

type NginxSeriesRes struct {
	Labels    []string `json:"labels"`
	Visitors  []int    `json:"visitors"`
	Pageviews []int    `json:"pageviews"`
	PvMinusUv []int    `json:"pvMinusUv"`
}

type AdClicksReq struct {
	g.Meta    `path:"/ad/clicks" tags:"广告点击统计" method:"post" summary:"广告点击统计"`
	StartTime int64 `json:"startTime"    dc:"开始时间"`
	EndTime   int64 `json:"endTime"    dc:"结束时间"`
	ListReq
}

type AdClicksRes struct {
	ListRes
	List []AdClicksItem `json:"list" dc:"点击统计列表"`
}

type AdClicksItem struct {
	Id        int    `json:"id" dc:"广告分组名称"`
	Name      string `json:"name" dc:"广告分组名称"`
	Today     int    `json:"today" dc:"今天"`
	Yesterday int    `json:"yesterday" dc:"昨天"`
	Week      int    `json:"week" dc:"本周"`
	LastWeek  int    `json:"lastWeek" dc:"上周"`
	Month     int    `json:"month" dc:"本月"`
	LastMonth int    `json:"lastMonth" dc:"上月"`
}
type AdClicksExportReq struct {
	g.Meta    `path:"/ad/clicksExport" tags:"广告点击统计导出" method:"post" summary:"广告点击统计导出"`
	StartTime int64 `json:"startTime"    dc:"开始时间"`
	EndTime   int64 `json:"endTime"    dc:"结束时间"`
	ListReq
}
type AdClicksExportItem struct {
	Id     int    `json:"id" dc:"广告分组id"`
	Name   string `json:"name" dc:"广告分组名称"`
	Clicks int    `json:"clicks" dc:"总点击量"`
}
