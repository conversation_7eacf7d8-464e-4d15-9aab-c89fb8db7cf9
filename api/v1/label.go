package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
	"gtcms/internal/model/entity"
)

type LabelListReq struct {
	g.Meta      `path:"/label/list" tags:"标签管理" method:"post" summary:"标签列表"`
	Name        *string `json:"name" dc:"名称"`
	Status      *int    `json:"status"       dc:"状态(1:显示 2:隐藏)"`
	IsRecommend *int    `json:"isRecommend"       dc:"推荐(1:是 2:否)"`
	Belong      int     `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId    *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id"`
	ListReq
}

type LabelListRes struct {
	ListRes
	List []LabelItem `json:"list" dc:"列表"`
}

type LabelItem struct {
	entity.Label
	ChooseStatus int `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
}

type LabelAddReq struct {
	g.Meta      `path:"/label/add" tags:"标签管理" method:"post" summary:"添加标签"`
	Name        string `v:"required" json:"name"         dc:"名称"`
	Sort        int    `json:"sort"         dc:"排序"`
	IsUsed      int    `json:"isUsed"       dc:"是否常用(1:是 2:否)" d:"2"`
	IsRecommend int    `json:"isRecommend"  dc:"是否推荐(1:是 2:否)" d:"2"`
	Status      int    `json:"status"       dc:"状态(1:显示 2:隐藏)" d:"1"`
	SeoTitle    string `json:"seoTitle"     dc:"seo标题"`
	SeoKeyword  string `json:"seoKeyword"   dc:"seo关键词"`
	SeoDesc     string `json:"seoDesc"      dc:"seo描述"`
	Thumb       string `json:"thumb"        dc:"缩略图"`
	CreateTime  int64  `json:"createTime"   dc:"创建时间"`
	Belong      int    `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId    *uint  `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	Url         string `v:"required" json:"url"          dc:"url"`
}

type LabelAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type LabelEditReq struct {
	g.Meta      `path:"/label/edit" tags:"标签管理" method:"post" summary:"编辑标签"`
	Id          uint    `v:"required" json:"id"         dc:"id"`
	Name        *string `json:"name"         dc:"名称"`
	Sort        *int    `json:"sort"         dc:"排序"`
	IsUsed      *int    `json:"isUsed"       dc:"是否常用(1:是 2:否)"`
	IsRecommend *int    `json:"isRecommend"  dc:"是否推荐(1:是 2:否)"`
	Status      *int    `json:"status"       dc:"状态(1:显示 2:隐藏)"`
	SeoTitle    *string `json:"seoTitle"     dc:"seo标题"`
	SeoKeyword  *string `json:"seoKeyword"   dc:"seo关键词"`
	SeoDesc     *string `json:"seoDesc"      dc:"seo描述"`
	Thumb       *string `json:"thumb"        dc:"缩略图"`
	Url         *string `json:"url"          dc:"url"`
	Belong      int     `v:"required" json:"belong" dc:"所属功能(0:标签 1:分组标签 2:站点标签)"`
}

type LabelDeleteReq struct {
	g.Meta `path:"/label/delete" tags:"标签管理" method:"post" summary:"删除标签"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
	Belong int    `v:"required" json:"belong" dc:"所属功能(0:标签 1:分组标签 2:站点标签)"`
}

type LabelOneReq struct {
	g.Meta `path:"/label/one" method:"post" tags:"标签管理" summary:"标签->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type LabelOneRes struct {
	LabelItem
}

type LabelOptionsReq struct {
	g.Meta `path:"/label/options" method:"post" tags:"标签管理" summary:"标签->选项（供选择器）"`
}

type LabelOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}

type LabelRelatedReq struct {
	g.Meta      `path:"/label/related" method:"post" tags:"标签管理" summary:"关联-增量"`
	LabelId     uint                 `v:"required" json:"labelId" dc:"标签id"`
	RelatedList []model.LabelRelated `v:"required" json:"relatedList" dc:"关联列表"`
	Belong      int                  `v:"required" json:"belong" dc:"所属功能(0:标签 1:分组标签 2:站点标签)"`
	BelongId    *uint                `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
}

type LabelNewsListReq struct {
	g.Meta `path:"/label/newsList" method:"post" tags:"标签管理" summary:"标签->文章列表"`
	ListReq
	LabelId    uint    `v:"required" json:"labelId"    dc:"标签id"`
	GroupId    *uint   `json:"groupId"    dc:"所属分组"`
	SiteName   *string `json:"siteName"    dc:"站点名称"`
	ColumnName *string `json:"columnName"    dc:"栏目名称"`
	Content    *string `json:"content"    dc:"文档内容"`
}

type LabelNewsItem struct {
	Id              uint   `json:"id"                dc:""`
	BelongColName   string `json:"belongColName" dc:"所属栏目名称"`
	BelongGroupName string `json:"belongGroupName" dc:"所属组"`
	BelongSiteName  string `json:"belongSiteName" dc:"所属站点"`
	Title           string `json:"title"       dc:"标题"`
	CreateTime      int64  `json:"createTime"  dc:"发布时间"`
	Views           int    `json:"views"  dc:"浏览量"`
	Status          int    `json:"status"  dc:"勾选状态(1:勾选 2:否)"`
	Creater         uint   `json:"creater"      dc:"创建者"`
}

type LabelNewsListRes struct {
	ListRes
	List []LabelNewsItem `json:"list" dc:"列表"`
}

type LabelGroupSiteAllListReq struct {
	g.Meta   `path:"/label/groupSiteAllList" tags:"标签管理" method:"post" summary:"分组/站点标签列表(全部)"`
	Belong   int   `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint `v:"required" json:"belongId" dc:"所属功能id"`
	Status   *int  `json:"status" dc:"状态(1:显示 2:隐藏)"`
	ListReq
}

type LabelGroupSiteAllListRes struct {
	ListRes
	List []LabelItem `json:"list" dc:"列表"`
}

type LabelGroupSiteAddReq struct {
	g.Meta `path:"/label/groupSiteAdd" tags:"标签管理" method:"post" summary:"分组/站点标签添加"`
	model.GroupSiteAddReqIn
}
