package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model/entity"
)

type RiskControlTabListReq struct {
	g.Meta `path:"/riskControlTab/list" method:"post" tags:"站群风控" summary:"栏目列表"`
}

type RiskControlTabListRes struct {
	List []*RiskTabItem `json:"list" dc:"列表"`
}

type RiskTabItem struct {
	entity.RiskControlTab
	Id       uint   `json:"id"            dc:""`
	TabType  int    `json:"tabType"       dc:"栏目分类[1 IP内容, 2 IP黑名单, 3 UA内容, 4 UA黑名单,  5 地区屏蔽,  6 CC防御]"`
	TabName  string `json:"tabName"       dc:"栏目名称"`
	IsOpen   int    `json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	IsList   int    `json:"isList"        dc:"栏目内容 [ 1 列表  2 功能配置 ]"`
	Metas    string `json:"metas"         dc:"功能属性 地区屏蔽 [{\"Language\": \"cn\", \"BlockChina\": 1, \"BlockNonChina\": 1}, {\"Language\": \"id\", \"BlockChina\": 1, \"BlockNonChina\": 1}]  CC防御 {\"Second\": 1, \"DefenseSwitch\": 1, \"TriggerFrequency\": 1} "`
	Priority int    `json:"priority"      dc:"访问优先级"`
}

type RiskControlTabEditSwitchReq struct {
	g.Meta  `path:"/riskControlTab/editSwitch" method:"post" tags:"站群风控" summary:"状态设置"`
	TabType uint `v:"required|in:1,2,3,4" json:"tabType"       dc:"栏目分类 针对栏目：1 IP内容, 2 IP黑名单, 3 UA内容, 4 UA黑名单"`
	IsOpen  int  `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
}

type RiskControlTabEditMetasReq struct {
	g.Meta  `path:"/riskControlTab/editMetas" method:"post" tags:"站群风控" summary:"功能设置"`
	TabType uint   `v:"required|in:5,6" json:"tabType"            dc:"栏目分类 针对栏目：5地区屏蔽， 6CC防御"`
	Metas   string `v:"required" json:"metas"         dc:"功能属性"`
}

type RiskControlContentListReq struct {
	g.Meta    `path:"/riskControlContent/list" method:"post" tags:"站群风控" summary:"内容列表"`
	TabType   int    `v:"required" json:"tabType"       dc:"栏目分类[1 IP内容, 2 IP黑名单, 3 UA内容, 4 UA黑名单]"`
	Key       string `v:"length:0,2000" json:"key" dc:"搜索关键字：对应\"IP\", \"UA关键字\" "`
	IsOpen    int    ` json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	StartTime int64  `json:"startTime" dc:"开始时间"`
	EndTime   int64  `json:"endTime" dc:"结束时间"`
	ListReq
}

type RiskControlContentListRes struct {
	ListRes
	List []*RiskContentItem `json:"list" dc:"列表"`
}

type RiskContentItem struct {
	Id            uint   `json:"id"            dc:""`
	TabType       int    `v:"required|in:1,2,3,4" json:"tabType"       dc:"分类 [1 IP内容, 2 IP黑名单, 3 UA内容, 4 UA黑名单 ]"`
	Content       string `v:"required" json:"content"       dc:"（1）tab_type=1时对应IP内容地址；（2）tab_type= 时 对应 IP黑名单地址（3）tab_type=3时 对应UA内容配置（4） tab_type=4时对应UA黑名单配置（5）tab_type=5时对应地区屏蔽配置 格式要求如下：{\"BlockUserSwitch\": 1, \"BlockSpiderSwitch\": 1}  屏蔽用户：1开 2关；屏蔽蜘蛛：1开 2关 （6）tab_type=6时 对应CC防御配置 格式要求如下：{\"DefenseSwitch\": 1, \"TriggerFrequency\": 1} 防御开关 ：1开 2关 触发频率：90-150左右"`
	IsOpen        int    `v:"required|in:1,2" json:"isOpen"        dc:"状态 [ 1 开 2 关]"`
	Remark        string `json:"remark"        dc:"备注"`
	CreateTime    int64  `json:"createTime"    dc:"创建时间"`
	CreateAccount string `json:"createAccount" dc:"创建者"`
	UpdateTime    int64  `json:"updateTime"    dc:"更新时间"`
	UpdateAccount string `json:"updateAccount" dc:"更新者"`
}

type RiskControlContentAddReq struct {
	g.Meta `path:"/riskControlContent/add" method:"post" tags:"站群风控" summary:"内容新增"`
	RiskContentItem
}

type RiskControlContentEditReq struct {
	g.Meta `path:"/riskControlContent/edit" method:"post" tags:"站群风控" summary:"内容编辑"`
	RiskContentItem
}

type RiskControlContentDeleteReq struct {
	g.Meta `path:"/riskControlContent/delete" method:"post" tags:"站群风控" summary:"内容删除"`
	Id     uint `json:"id" dc:"主键id" v:"required"`
}
