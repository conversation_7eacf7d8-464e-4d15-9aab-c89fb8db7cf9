package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
	"gtcms/internal/model/entity"
)

type RoleAddReq struct {
	g.Meta        `path:"/role/add" method:"post" tags:"角色管理" summary:"添加角色"`
	Name          string `v:"required" json:"name" dc:"角色编号"`
	Label         string `v:"required" json:"label" dc:"角色名称（中）"`
	Describe      string `json:"describe" dc:"角色描述"`
	PermissionIds []uint `v:"required" json:"permissionId" dc:"角色拥有的权限id"`
	Status        uint   `v:"required" json:"status" dc:"状态:1.启用;2.禁用"`
}

type RoleEditReq struct {
	g.Meta        `path:"/role/edit" method:"post" tags:"角色管理" summary:"修改角色"`
	Id            uint    `v:"required|min:0" json:"id" dc:"角色id"`
	Name          *string `json:"name" dc:"角色编号"`
	Label         *string `json:"label" dc:"角色名称（中）"`
	Describe      *string `json:"describe" dc:"角色描述"`
	PermissionIds []uint  `v:"required" json:"permissionId" dc:"角色拥有的权限id"`
	Status        uint    `v:"required" json:"status" dc:"状态:1.启用;2.禁用"`
}

type RoleDeleteReq struct {
	g.Meta `path:"/role/delete" method:"post" tags:"角色管理" summary:"删除角色"`
	Id     uint `v:"required|min:0" json:"id" dc:"角色id"`
}

type RoleItem struct {
	*entity.Role
	PermissionIds   []uint           `json:"permissionIds" dc:"角色拥有的权限id数组"`
	PermissionTree  []PermissionItem `json:"permissionTree" dc:"角色拥有的权限树结构"`
	PermissionNames []string         `json:"permissionNames" dc:"角色拥有的权限id对应的name"`
}

type RoleListReq struct {
	g.Meta `path:"/role/list" method:"post" tags:"角色管理" summary:"获取角色列表"`
	ListReq
	Name string `json:"name" dc:"角色名称"`
}
type RoleListRes struct {
	ListRes
	List []RoleItem `json:"list"`
}

type RoleOneReq struct {
	g.Meta `path:"/role/one" method:"post" tags:"角色管理" summary:"获取角色"`
	Id     uint `v:"required" json:"id" dc:"角色id"`
}
type RoleOneRes RoleItem

type RoleOptionsReq struct {
	g.Meta `path:"/role/options" method:"post" tags:"角色管理" summary:"选择器"`
}
type RoleOptionsRes struct {
	Options []model.SelectOption `json:"options" dc:"选择器选项数组"`
}
