package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"gtcms/internal/model"
	"gtcms/internal/model/entity"
)

type FriendlyLinkListReq struct {
	g.Meta   `path:"/friendlyLink/list" tags:"友链管理" method:"post" summary:"友链列表"`
	Name     *string   `json:"name" dc:"名称"`
	Type     *int      `json:"type"         dc:"类型(1:文字 2:图片)"`
	Belong   *int      `json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId *uint     `json:"belongId" dc:"所属功能id"`
	Urls     *[]string `json:"urls" dc:"链接url"`
	ListReq
}

type FriendlyLinkListRes struct {
	ListRes
	List []FriendlyLinkItem `json:"list" dc:"列表"`
}

type FriendlyLinkItem struct {
	entity.FriendlyLink
	ChooseStatus int    `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
	GroupName    string `json:"groupName" dc:"分组名"`
}

type FriendlyLinkAddReq struct {
	g.Meta       `path:"/friendlyLink/add" tags:"友链管理" method:"post" summary:"添加友链"`
	Type         int     `v:"required|in:1,2" json:"type"         dc:"类型(1:文字 2:图片)"`
	Name         string  `v:"required" json:"name"         dc:"名称"`
	Url          string  `v:"required" json:"url"          dc:"链接"`
	ExpiredTime  int64   `v:"required" json:"expiredTime"  dc:"链接有限期"`
	Resource     string  `json:"resource"     dc:"链接来源"`
	WebType      int64   `json:"webType"      dc:"网站类型(1: 企业 2:私人)"`
	BaiduWeights float64 `json:"baiduWeights" dc:"百度权重"`
	Sort         int     `json:"sort"         dc:"排序"`
	Property     int64   `json:"property"     dc:"链接属性(1:新窗口打开 2:nofollow)"`
	Remark       string  `json:"remark"       dc:"备注"`
	ImageSize    string  `json:"imageSize"    dc:"图片尺寸"`
	Image        string  `v:"required-if:type,2" json:"image"        dc:"图片"`
	Belong       int     `v:"required" json:"belong" dc:"所属功能(0:栏目 1:组 2:站点)"`
	BelongId     *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	LinkType     int     `json:"linkType"         dc:"友链类型(1:分组新增 2:手动新增)"`
}

type FriendlyLinkAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type FriendlyLinkEditReq struct {
	g.Meta       `path:"/friendlyLink/edit" tags:"友链管理" method:"post" summary:"编辑友链"`
	Id           uint     `v:"required" json:"id"         dc:"id"`
	Type         *int     `json:"type"         dc:"类型(1:文字 2:图片)"`
	Name         *string  `json:"name"         dc:"名称"`
	Url          *string  `json:"url"          dc:"链接"`
	ExpiredTime  *int64   `json:"expiredTime"  dc:"链接有限期"`
	Resource     *string  `json:"resource"     dc:"链接来源"`
	WebType      *int64   `json:"webType"      dc:"网站类型(1: 企业 2:私人)"`
	BaiduWeights *float64 `json:"baiduWeights" dc:"百度权重"`
	Sort         *int     `json:"sort"         dc:"排序"`
	Property     *int64   `json:"property"     dc:"链接属性(1:新窗口打开 2:nofollow)"`
	Remark       *string  `json:"remark"       dc:"备注"`
	ImageSize    *string  `json:"imageSize"    dc:"图片尺寸"`
	Image        *string  `json:"image"        dc:"图片"`
}

type FriendlyLinkDeleteReq struct {
	g.Meta `path:"/friendlyLink/delete" tags:"友链管理" method:"post" summary:"删除友链"`
	Id     uint   `json:"id"         dc:"id"`
	Ids    []uint `json:"ids"         dc:"ids"`
}

type FriendlyLinkOneReq struct {
	g.Meta `path:"/friendlyLink/one" method:"post" tags:"友链管理" summary:"友链->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type FriendlyLinkOneRes struct {
	FriendlyLinkItem
}

type FriendlyLinkGroupSiteAllListReq struct {
	g.Meta   `path:"/friendlyLink/groupSiteAllList" tags:"友链管理" method:"post" summary:"分组/站点友链列表(全部)"`
	Belong   *int    `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint   `v:"required" json:"belongId" dc:"所属功能id"`
	Name     *string `json:"name" dc:"名称"`
	Type     *int    `json:"type"         dc:"类型(1:文字 2:图片)"`
	ListReq
}

type FriendlyLinkGroupSiteAllListRes struct {
	ListRes
	List []FriendlyLinkItem `json:"list" dc:"列表"`
}

type FriendlyLinkGroupSiteAddReq struct {
	g.Meta `path:"/friendlyLink/groupSiteAdd" tags:"友链管理" method:"post" summary:"分组/站点友链添加"`
	model.GroupSiteAddReqIn
}

type FriendlyLinkRuleReq struct {
	ListReq
	g.Meta `path:"/friendlyLink/rule" tags:"友链管理" method:"post" summary:"友链单链互链列表"`
	Type   int `json:"type" dc:"类型(1:单链 2:互链)"`
}

type FriendlyLinkRuleRes struct {
	ListRes
	List []*FriendlyLinkRuleItem `json:"list" dc:"列表"`
}

type FriendlyLinkRuleItem struct {
	entity.FriendlyLinkRule
	SiteGroupSourceName string `json:"siteGroupSourceName" dc:"来源组名称"`
	SiteGroupTargetName string `json:"siteGroupTargetName" dc:"目标组名称"`
}

type FriendlyLinkRuleAddReq struct {
	g.Meta        `path:"/friendlyLink/ruleAdd" tags:"友链管理" method:"post" summary:"友链单链互链新增"`
	Type          int  `json:"type" dc:"类型(1:单链 2:互链)"`
	SourceId      uint `v:"required" json:"sourceId" dc:"来源id"`
	TargetId      uint `v:"required" json:"targetId" dc:"目标id"`
	FriendlyLinks uint `json:"friendlyLinks" dc:"友链数量"`
}

type FriendlyLinkRuleEditReq struct {
	g.Meta        `path:"/friendlyLink/ruleEdit" tags:"友链管理" method:"post" summary:"友链单链互链编辑"`
	Id            uint `v:"required" json:"id" dc:"规则id"`
	FriendlyLinks uint `v:"required" json:"friendlyLinks" dc:"友链数量"`
	ExpiredTime   uint ` json:"expiredTime" dc:"链接有限期一年"`

	//Type     int  `v:"required|in:1,2" json:"type" dc:"类型(1:单链 2:互链)"`
	//SourceId uint `v:"required" json:"sourceId" dc:"来源id"`
	//TargetId uint `v:"required" json:"targetId" dc:"目标id"`
}

type FriendlyLinkRuleAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type FriendlyLinkRuleEditRes struct {
}

type FriendlyLinkRuleDeleteReq struct {
	g.Meta `path:"/friendlyLink/ruleDelete" tags:"友链管理" method:"post" summary:"友链单链互链删除"`
	Id     uint `json:"id" dc:""`
}
