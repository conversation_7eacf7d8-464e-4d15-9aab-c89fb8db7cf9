package v1

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"gtcms/internal/model"
)

type UrlSettingListReq struct {
	g.Meta   `path:"/urlSetting/list" tags:"url设置管理" method:"post" summary:"url设置列表"`
	Belong   int     `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId *uint   `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id"`
	Status   *int    `json:"status"       dc:"状态(1:启用 2:禁用)"`
	Name     *string `json:"name" dc:"名称"`
	ListReq
}

type UrlSettingListRes struct {
	ListRes
	List []UrlSettingItem `json:"list" dc:"url设置列表"`
}

type UrlSettingItem struct {
	Id           uint               `json:"id"           dc:""`
	Name         string             `v:"required" json:"name" dc:"名称"`
	<PERSON>ong       int                `json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId     uint               `json:"belongId" dc:"所属分组/站点id"`
	Remark       string             `json:"remark"       dc:"标题模版说明"`
	Mode         int                `json:"mode"          dc:"模式(1:动态url 2:伪静态 3:静态页面)"`
	StaticSuffix string             `json:"staticSuffix"  dc:"静态后缀"`
	Config       []UrlSettingConfig `json:"config"       dc:"标题模版内容"`
	Status       int                `json:"status"        dc:"状态(1:启用 2:禁用)"`
	CreateTime   int64              `json:"createTime"    dc:"创建时间"`
	UpdateTime   int64              `json:"updateTime"    dc:"更新时间"`
	SelfId       uint               `json:"selfId"       dc:"自身id(如果belong非0,那这个指向的是id)"`
	ChooseStatus int                `json:"chooseStatus" dc:"选中状态(1:选中 2:未选中)"`
	Creater      uint               `json:"creater"      dc:"创建者"`
	IsDefault    int                `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	Demo         string             `json:"demo"          dc:"演示例子"`
}

type UrlSettingAddReq struct {
	g.Meta       `path:"/urlSetting/add" tags:"url设置管理" method:"post" summary:"添加url设置"`
	Name         string             `v:"required" json:"name" dc:"名称"`
	Belong       int                `v:"required" json:"belong" dc:"所属功能(0:基础库 1:组 2:站点)"`
	BelongId     *uint              `v:"required-if:belong,1,belong,2" json:"belongId" dc:"所属功能id，belong非0时，需传递"`
	Remark       string             `json:"remark"       dc:"标题模版说明"`
	Mode         int                `json:"mode"          dc:"模式(1:动态url 2:伪静态 3:静态页面)"`
	StaticSuffix string             `json:"staticSuffix"  dc:"静态后缀"`
	Config       []UrlSettingConfig `json:"config"       dc:"标题模版内容" d:"[]"`
	Status       int                `json:"status"        dc:"状态(1:启用 2:禁用)"`
	IsDefault    int                `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	Demo         string             `json:"demo"          dc:"演示例子"`
}

type UrlSettingConfig struct {
	Category int                    `json:"category" dc:"1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:专题 9:单页 10:球队 11:球员 12:集锦栏目 13:集锦详情页 14:积分榜 15:射手榜 16:资料库 17:资料库详情 18:专题详情 19:电视频道 20:电视频道详情 21:赛果 22:赛果详情"`
	Body     []UrlSettingConfigBody `json:"body" dc:"内容"`
}

type UrlSettingConfigBody struct {
	Content   string `json:"content" dc:"内容"`
	IsDefault int    `json:"default" dc:"是否默认(1:是 2:否)"`
	Mode      int    `json:"mode"    dc:"模式(1:继承模式 2:根目录模式 3:自定义模式)"`
	Displays  string `json:"displays" dc:"演示"`
	Delimiter string `json:"delimiter"    dc:"分隔符"`
	Sort      int    `json:"sort"    dc:"正反序(1正 2反)"`
	ColMode   int    `json:"colMode"    dc:"栏目模式(1一级 2二级)"`
}

type UrlSettingAddRes struct {
	Id uint `json:"id" dc:"新添记录的id"`
}

type UrlSettingEditReq struct {
	g.Meta       `path:"/urlSetting/edit" tags:"url设置管理" method:"post" summary:"编辑url设置"`
	Id           uint                `v:"required" json:"id"         dc:"id"`
	Name         *string             `json:"name"       dc:"名称"`
	Config       *[]UrlSettingConfig `json:"config"       dc:"标题模版内容"`
	Mode         *int                `json:"mode"          dc:"模式(1:动态url 2:伪静态 3:静态页面)"`
	StaticSuffix *string             `json:"staticSuffix"  dc:"静态后缀"`
	Status       *int                `json:"status"        dc:"状态(1:启用 2:禁用)"`
	IsDefault    *int                `json:"isDefault"     dc:"是否默认(1:是 2:否)"`
	Demo         *string             `json:"demo"          dc:"演示例子"`
}

type UrlSettingDeleteReq struct {
	g.Meta `path:"/urlSetting/delete" tags:"url设置管理" method:"post" summary:"删除url设置"`
	Id     uint `v:"required" json:"id"         dc:"id"`
}

type UrlSettingOneReq struct {
	g.Meta `path:"/urlSetting/one" method:"post" tags:"url设置管理" summary:"url设置->获取"`
	Id     uint `v:"required" json:"id" dc:"id"`
}
type UrlSettingOneRes struct {
	UrlSettingItem
}

type UrlSettingFastFillReq struct {
	g.Meta   `path:"/urlSetting/fastFill" method:"post" tags:"url设置管理" summary:"使用url配置快速填充内容"`
	Category int  `v:"required" json:"category" dc:"1:赛程栏目 2:录像栏目 3:新闻栏目 4:标签 5:赛程详情页 6:录像详情页 7:新闻详情页 8:专题 9:单页 10:球队 11:球员"`
	SiteId   uint `json:"siteId" dc:"站点id"`
}
type UrlSettingFastFillRes struct {
	Content string `json:"content" dc:"内容数据"`
}

type UrlSettingGroupSiteAllListReq struct {
	g.Meta   `path:"/urlSetting/groupSiteAllList" tags:"url设置管理" method:"post" summary:"分组/站点url库(全部)"`
	Belong   int   `v:"required" json:"belong" dc:"所属功能(1:组 2:站点)"`
	BelongId *uint `v:"required" json:"belongId" dc:"所属功能id"`
	ListReq
}

type UrlSettingGroupSiteAllListRes struct {
	ListRes
	List []UrlSettingItem `json:"list" dc:"列表"`
}

type UrlSettingGroupSiteAddReq struct {
	g.Meta `path:"/urlSetting/groupSiteAdd" tags:"url设置管理" method:"post" summary:"分组/站点url添加"`
	model.GroupSiteAddReqIn
}

type UrlSettingImportReq struct {
	g.Meta `path:"/urlSetting/import" method:"post" mime:"multipart/form-data" tags:"url设置管理" summary:"批量新建"`
	File   *ghttp.UploadFile `json:"file" type:"file" dc:"选择导入文件"`
}

type UrlSettingExportReq struct {
	g.Meta `path:"/urlSetting/export" method:"post" tags:"url设置管理" summary:"导出"`
	Name   *string `json:"name"       dc:"名称"`
	Ids    []uint  `json:"ids"        dc:"ids"`
}
