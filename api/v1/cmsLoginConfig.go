package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

type CmsLoginConfigDetailReq struct {
	g.Meta `path:"/cmsLoginConfig/detail" method:"post" tags:"后台设置" summary:"基本配置-详情"`
}
type CmsLoginConfigDetailRes struct {
	CmsLoginConfigVo
}

type CmsLoginConfigEditReq struct {
	g.Meta       `path:"/cmsLoginConfig/edit" method:"post" tags:"后台设置" summary:"基本配置-编辑"`
	Id           uint   `v:"required" json:"id"            dc:""`
	IsOpen       int    `v:"in:1,2" json:"isOpen"        dc:"后台开关： 1:开 2:关"`
	Title        string `json:"title"         dc:"后台名称"`
	Url          string `json:"url"           dc:"后台网址"`
	IsIpBind     int    `v:"in:1,2" json:"isIpBind"      dc:"IP绑定： 1:开 2:关"`
	IsMacBind    int    `v:"in:1,2" json:"isMacBind"     dc:"机器码绑定： 1:开 2:关"`
	IsGoogleBind int    `v:"in:1,2" json:"isGoogleBind"  dc:"谷歌验证码绑定： 1:开 2:关"`
}

type CmsLoginConfigVo struct {
	Id           uint   `json:"id"            dc:""`
	IsOpen       int    `json:"isOpen"        dc:"后台开关： 1:开 2:关"`
	Title        string `json:"title"         dc:"后台名称"`
	Url          string `json:"url"           dc:"后台网址"`
	IsIpBind     int    `json:"isIpBind"      dc:"IP绑定： 1:开 2:关"`
	IsMacBind    int    `json:"isMacBind"     dc:"机器码绑定： 1:开 2:关"`
	IsGoogleBind int    `json:"isGoogleBind"  dc:"谷歌验证码绑定： 1:开 2:关"`
}

type CmsConfigDetailReq struct {
	g.Meta `path:"/cmsConfig/detail" method:"post" tags:"后台设置" summary:"公共配置"`
}

type CmsConfigDetailRes struct {
	LoginConfig CmsLoginConfig `json:"loginConfig" dc:"登录配置"`
}

type CmsLoginConfig struct {
	Title string `json:"title"         dc:"后台名称"`
}

// 根对象
type Response struct {
	Code   int    `json:"code"`
	Status string `json:"status"`
	Data   Data   `json:"data"`
}

// data 节点
type Data struct {
	Number int           `json:"number"`
	Ayahs  []Ayah        `json:"ayahs"`
	Surahs map[int]Surah `json:"surahs"`
}

// ayahs 数组元素
type Ayah struct {
	Number        int    `json:"number"`
	Text          string `json:"text"`
	Surah         Surah  `json:"surah"`
	NumberInSurah int    `json:"numberInSurah"`
	Juz           int    `json:"juz"`
	Manzil        int    `json:"manzil"`
	Page          int    `json:"page"`
	Ruku          int    `json:"ruku"`
	HizbQuarter   int    `json:"hizbQuarter"`
	Sajda         bool   `json:"sajda"`
}

// surahs 映射值
type Surah struct {
	Number                 int    `json:"number"`
	Name                   string `json:"name"`
	EnglishName            string `json:"englishName"`
	EnglishNameTranslation string `json:"englishNameTranslation"`
	RevelationType         string `json:"revelationType"`
	NumberOfAyahs          int    `json:"numberOfAyahs"`
}
