--2024-11-05  后台站点管理,增加站点收录项(通过api查询接口获取)  by max

alter table site add   column `baidu` int unsigned NOT NULL default 0 COMMENT '百度收录的数量';
alter table site add   column `sougou` int unsigned NOT NULL default 0 COMMENT '搜狗收录的数量';



CREATE TABLE `crawler_news_comment` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `source` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '来源 （比如新浪体育',
    `mid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '消息id',
    `docid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '在原始来源处的唯一id',
    `content` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '评论内容',
    `competition_id` int NOT NULL DEFAULT '0' COMMENT '关联的赛事id,  为0表示还未关联',
    `lid` int NOT NULL DEFAULT '0' COMMENT '关联的赛事id,  为0表示还未关联',
    `create_time` bigint DEFAULT NULL,
    `update_time` bigint NOT NULL,
    `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型。 1足球，2篮球',
    PRIMARY KEY (`id`),
    UNIQUE KEY `crawler_news_raw_pk2` (`source`,`mid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='新闻评论表';



alter table crawler_news_raw add   column `language` varchar(16) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT 'cn' COMMENT '语言-cn中文 en英文';


alter table site_group add   column   `model_fake` tinyint NOT NULL DEFAULT '0' COMMENT '模版伪原创(1-纯数字，2-纯字母，3-数字+字母，4-字母+数字)';
alter table site_group add   column   `model_fake_bit` int NOT NULL DEFAULT '0'  COMMENT '模版伪原创的位数';
alter table site add   column   `domain_jump` tinyint NOT NULL DEFAULT '0' COMMENT '域名跳转(1-www跳@，2-@跳www)';


alter table site_group add   column    `open_friendly_link` tinyint NOT NULL DEFAULT '0' COMMENT '开启手动友链(1是 2否)';
alter table site_group add   column    `friendly_links` int NOT NULL DEFAULT '0' COMMENT '友链数量';

alter table friendly_link add   column  `link_type` tinyint NOT NULL DEFAULT '1' COMMENT '友链类型(1:分组新增 2:手动新增)'

-- //删除
alter table site_group drop column  `open_friendly_link` ;
alter table site_group drop  column    `friendly_links`;

alter table friendly_link_rule add   column  `link_type` tinyint NOT NULL DEFAULT '1' COMMENT '友链类型(1:分组新增 2:手动新增)'

alter table friendly_link_rule add   column `friendly_links` int unsigned NOT NULL DEFAULT '0' COMMENT '友链数量';


alter table friendly_link_rule add   column   `expired_time` bigint NOT NULL DEFAULT '0' COMMENT '链接有限期';
alter table friendly_link add   column   `rule_id` bigint NOT NULL DEFAULT '0' COMMENT '规则id';




CREATE TABLE `video_product_child` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `pid` bigint NOT NULL DEFAULT '0' COMMENT '直播源产品id',
    `cid` bigint NOT NULL DEFAULT '0' COMMENT '新生成的子id',
    `name` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '子名称',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='直播源产品关联表（方便统计查找源）';


alter table collect_basketball_competition_stats add   column `teams_stats` MEDIUMTEXT CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '球队数据字段说明
';
alter table collect_basketball_competition_stats add   column `players_stats` MEDIUMTEXT CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '球员数据字段说明';


alter table collect_football_competition_stats add   column `teams_stats` MEDIUMTEXT CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '球队数据字段说明
';
alter table collect_football_competition_stats add   column `players_stats` MEDIUMTEXT CHARACTER SET utf8mb3 COLLATE utf8mb3_bin COMMENT '球员数据字段说明';


CREATE TABLE `collect_football_match_analysis` (
   `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
   `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
   `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
   `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
   `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
   `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
   `goal_distribution` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '进球分布',
   PRIMARY KEY (`id`),
   UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球比赛分析数据';


alter table collect_football_match_analysis add   column `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩';



CREATE TABLE `collect_basketball_match_analysis` (
       `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
       `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
       `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
       `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
       `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
       `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
       `goal_distribution` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '进球分布',
       `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩',
       PRIMARY KEY (`id`),
       UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球比赛分析数据';




CREATE TABLE `seo_rank` (
     `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
     `url` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'url',
     `keywords` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '关键词',
     `taskid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '任务id',
     `json` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '返回json',
     `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
     `has_get` int NOT NULL DEFAULT '0' COMMENT '0-未获取结果，1-已获取结果',
     `type` int NOT NULL DEFAULT '0' COMMENT '0-pc，1-mobile',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='排名查询';



alter table seo_rank add   column `uuid` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'uuid';
alter table seo_rank add   column `send_at` bigint NOT NULL DEFAULT '0' COMMENT '发送查询时间';
alter table seo_rank add   column `created_at` bigint NOT NULL DEFAULT '0' COMMENT '插入时间';
alter table seo_rank add   column `has_send` int NOT NULL DEFAULT '0' COMMENT '0-未发送，1-已发送';
alter table seo_rank add   column `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者id';
alter table seo_rank add   column `has_retry` int unsigned NOT NULL DEFAULT '0' COMMENT '0-未重试，1-已重试';
alter table seo_rank add   column `old_taskid` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT 'old任务id';
alter table seo_rank add   column `is_rank` int NOT NULL DEFAULT '0' COMMENT '查询类型 0-普通查询，1-关键词排名';

alter table seo_rank add   column `delete_at` bigint NOT NULL DEFAULT '0' COMMENT '删除时间';




DROP TABLE IF EXISTS `collect_basketball_match_live`;
CREATE TABLE `collect_basketball_match_live` (
     `id` int NOT NULL COMMENT '赛事id',
     `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
     `timer` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
     `status` int NOT NULL DEFAULT '0' COMMENT '赛事状态',
     `seconds` int NOT NULL COMMENT '小节剩余时间(秒)',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='篮球实时统计';

DROP TABLE IF EXISTS `collect_football_match_live`;
CREATE TABLE `collect_football_match_live` (
   `id` int NOT NULL COMMENT '赛事id',
   `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
   `status` int NOT NULL DEFAULT '0',
   `minutes` int NOT NULL COMMENT '比赛进行分钟数',
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='足球实时统计';








-- 网球表
-- https://open.sportnanoapi.com/api/v5/tennis/match/list
--
CREATE TABLE `collect_tennis_match` (
      `id` bigint NOT NULL COMMENT '比赛id',
      `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
      `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
      `tournament_id` int NOT NULL DEFAULT '0' COMMENT '阶段id',
      `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
      `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
      `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
      `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
      `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
      `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
      `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
      `match_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '团体赛下关联的比赛',
      `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
      `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
      PRIMARY KEY (`id`),
      KEY `away_id` (`away_team_id`),
      KEY `home_id` (`home_team_id`),
      KEY `match_ts_index` (`match_ts`),
      KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛数据';



-- https://open.sportnanoapi.com/api/v5/tennis/match/season
CREATE TABLE `collect_tennis_schedule_diary` (
     `id` int NOT NULL COMMENT '比赛id',
     `season_id` int NOT NULL COMMENT '赛季id',
     `competition_id` int NOT NULL COMMENT '联赛id',
     `match_time` int DEFAULT NULL COMMENT '比赛时间',
     `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
     `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
     `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
     `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
--      `stage_id` int DEFAULT '0' COMMENT '阶段id',
--      `group_num` int DEFAULT '0' COMMENT '第几组，1-A、2-B以此类推',
--      `round_num` int DEFAULT '0' COMMENT '第几轮',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛程赛果';


-- https://open.sportnanoapi.com/api/v5/tennis/match/analysis
CREATE TABLE `collect_tennis_match_analysis` (
           `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
           `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
           `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
           `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
           `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
           `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
           `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩',
           PRIMARY KEY (`id`),
           UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛分析数据';


-- https://open.sportnanoapi.com/api/v5/tennis/match/analysis

-- https://open.sportnanoapi.com/api/v5/tennis/unique_tournament/list
CREATE TABLE `collect_tennis_competition` (
        `id` int NOT NULL,
        `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
        `parent_id` int NOT NULL DEFAULT '0' COMMENT '父赛事id（所属赛事）',
        `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
        `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
        `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

        `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
        `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
        `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',

        `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
        `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事表';


-- https://open.sportnanoapi.com/api/v5/tennis/venue/list

CREATE TABLE `collect_tennis_venue` (
          `id` int NOT NULL COMMENT '场馆id',
          `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
          `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
          `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
          `city_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市中文名称',
          `city_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市英文名称',
          `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='场馆';


-- https://open.sportnanoapi.com/api/v5/tennis/season/list
CREATE TABLE `collect_tennis_season` (
                   `id` int NOT NULL,
                   `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
                   `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季名称',
                   `abbr` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季简称',
                   `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
                   `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
                   `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
                   `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
                   `updated_at` bigint NOT NULL COMMENT '更新时间',
                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';



-- https://open.sportnanoapi.com/api/v5/tennis/team/list

CREATE TABLE `collect_tennis_team` (
         `id` int NOT NULL,
         `sub_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '双打说明(双打可能为空，未知)',
         `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型，1-单打、2-双打、0-未知',
         `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
         `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
         `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

         `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
         `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
         `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
         `abbr` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩写',

         `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'logo',
         `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别，1-男、2-女、0-未知',
         `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
         `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
         `extra` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '个人统计数据（没有数据，字段不存在）',
         `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
         `create_time` bigint NOT NULL,
         `update_time` bigint NOT NULL,
         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';

-- 获取赛季积分榜数据
-- https://open.sportnanoapi.com/api/v5/tennis/season/table/detail

CREATE TABLE `collect_tennis_competition_stats` (
      `id` int NOT NULL COMMENT '赛事id',
      `season_id` int NOT NULL,
      `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
      PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';







-- 羽毛球表
-- https://open.sportnanoapi.com/api/v1/badminton/match/list
--
CREATE TABLE `collect_badminton_match` (
    `id` bigint NOT NULL COMMENT '比赛id',
    `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
    `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
    `tournament_id` int NOT NULL DEFAULT '0' COMMENT '阶段id',
    `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
    `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
    `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
    `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
    `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
    `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
    `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
    `match_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '团体赛下关联的比赛',
    `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
    PRIMARY KEY (`id`),
    KEY `away_id` (`away_team_id`),
    KEY `home_id` (`home_team_id`),
    KEY `match_ts_index` (`match_ts`),
    KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛数据';






-- https://open.sportnanoapi.com/api/v1/badminton/match/diary
CREATE TABLE `collect_badminton_schedule_diary` (
        `id` int NOT NULL COMMENT '比赛id',
        `season_id` int NOT NULL COMMENT '赛季id',
        `competition_id` int NOT NULL COMMENT '联赛id',
        `match_time` int DEFAULT NULL COMMENT '比赛时间',
        `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
        `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
        `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
        `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
--      `stage_id` int DEFAULT '0' COMMENT '阶段id',
--      `group_num` int DEFAULT '0' COMMENT '第几组，1-A、2-B以此类推',
--      `round_num` int DEFAULT '0' COMMENT '第几轮',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛程赛果';

-- https://open.sportnanoapi.com/api/v5/tennis/match/analysis
-- CREATE TABLE `collect_football_match_analysis` (
--        `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
--        `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
--        `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
--        `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
--        `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
--        `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
--        `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩',
--        PRIMARY KEY (`id`),
--        UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛分析数据';



-- https://open.sportnanoapi.com/api/v1/badminton/unique_tournament/list
CREATE TABLE `collect_badminton_competition` (
    `id` int NOT NULL,
    `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
--     `parent_id` int NOT NULL DEFAULT '0' COMMENT '父赛事id（所属赛事）',
    `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
    `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
    `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

    `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
    `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
    `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',

    `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
    `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事表';


-- https://open.sportnanoapi.com/api/v1/badminton/venue/list
CREATE TABLE `collect_badminton_venue` (
    `id` int NOT NULL COMMENT '场馆id',
    `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
    `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
    `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
--     `city_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市中文名称',
--     `city_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市英文名称',
    `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='场馆';


-- https://open.sportnanoapi.com/api/v5/tennis/season/list
CREATE TABLE `collect_tennis_season` (
     `id` int NOT NULL,
     `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
     `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季名称',
     `abbr` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季简称',
     `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
     `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
     `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
     `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
     `updated_at` bigint NOT NULL COMMENT '更新时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';



-- https://open.sportnanoapi.com/api/v1/badminton/team/list

CREATE TABLE `collect_badminton_team` (
           `id` int NOT NULL,
           `sub_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '双打说明(双打可能为空，未知)',
           `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型，1-单打、2-双打、0-未知',
           `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
           `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
           `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

           `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
           `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
           `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
           `abbr` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩写',

           `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'logo',
           `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别，1-男、2-女、0-未知',
           `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
           `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
           `extra` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '个人统计数据（没有数据，字段不存在）',
           `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
           `create_time` bigint NOT NULL,
           `update_time` bigint NOT NULL,
           PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';

-- 获取赛季积分榜数据
-- https://open.sportnanoapi.com/api/v1/badminton/season/table/detail

CREATE TABLE `collect_badminton_competition_stats` (
                `id` int NOT NULL COMMENT '赛事id',
                `season_id` int NOT NULL,
                `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';








-- 排球表
-- https://open.sportnanoapi.com/api/v1/volleyball/match/list
--
CREATE TABLE `collect_volleyball_match` (
        `id` bigint NOT NULL COMMENT '比赛id',
        `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
        `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
        `tournament_id` int NOT NULL DEFAULT '0' COMMENT '阶段id',
        `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
        `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
        `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
        `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
        `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
        `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
        `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
        `match_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '团体赛下关联的比赛',
        `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
        `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
        PRIMARY KEY (`id`),
        KEY `away_id` (`away_team_id`),
        KEY `home_id` (`home_team_id`),
        KEY `match_ts_index` (`match_ts`),
        KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛数据';




-- https://open.sportnanoapi.com/api/v1/volleyball/match/diary
CREATE TABLE `collect_volleyball_schedule_diary` (
         `id` int NOT NULL COMMENT '比赛id',
         `season_id` int NOT NULL COMMENT '赛季id',
         `competition_id` int NOT NULL COMMENT '联赛id',
         `match_time` int DEFAULT NULL COMMENT '比赛时间',
         `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
         `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
         `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
         `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
--      `stage_id` int DEFAULT '0' COMMENT '阶段id',
--      `group_num` int DEFAULT '0' COMMENT '第几组，1-A、2-B以此类推',
--      `round_num` int DEFAULT '0' COMMENT '第几轮',
                                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛程赛果';

--
--
-- https://open.sportnanoapi.com/api/v5/tennis/match/analysis
-- CREATE TABLE `collect_football_match_analysis` (
--                                                    `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
--                                                    `match_id` int NOT NULL DEFAULT '0' COMMENT '比赛id',
--                                                    `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
--                                                    `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
--                                                    `team_id` int NOT NULL DEFAULT '0' COMMENT '队伍id',
--                                                    `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
--                                                    `history` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '近期战绩',
--                                                    PRIMARY KEY (`id`),
--                                                    UNIQUE KEY `match_team_pk2` (`match_id`,`team_id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛分析数据';


-- https://open.sportnanoapi.com/api/v1/volleyball/unique_tournament/list
CREATE TABLE `collect_volleyball_competition` (
          `id` int NOT NULL,
          `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
          `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
          `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
          `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
          `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

          `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
          `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
          `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',

          `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
          `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事表';

--
-- -- https://open.sportnanoapi.com/api/v5/tennis/venue/list
--
-- CREATE TABLE `collect_tennis_venue` (
--                                         `id` int NOT NULL COMMENT '场馆id',
--                                         `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
--                                         `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
--                                         `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',
--                                         `city_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市中文名称',
--                                         `city_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '城市英文名称',
--                                         `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
--                                         PRIMARY KEY (`id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='场馆';
--

-- https://open.sportnanoapi.com/api/v1/volleyball/season/list
CREATE TABLE `collect_volleyball_season` (
     `id` int NOT NULL,
     `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
--      `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季名称',
--      `abbr` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季简称',
     `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
--      `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
--      `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
--      `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
     `updated_at` bigint NOT NULL COMMENT '更新时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';



-- https://open.sportnanoapi.com/api/v1/volleyball/team/list

CREATE TABLE `collect_volleyball_team` (
       `id` int NOT NULL,
--        `sub_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '双打说明(双打可能为空，未知)',
--        `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型，1-单打、2-双打、0-未知',
       `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
       `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
       `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

       `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
       `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
       `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
--        `abbr` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩写',

       `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'logo',
       `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别，1-男、2-女、0-未知',
--        `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
--        `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
--        `extra` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '个人统计数据（没有数据，字段不存在）',
       `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
       `create_time` bigint NOT NULL,
       `update_time` bigint NOT NULL,
       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息';

-- https://open.sportnanoapi.com/api/v1/volleyball/season/table/detail

CREATE TABLE `collect_volleyball_competition_stats` (
        `id` int NOT NULL COMMENT '赛事id',
        `season_id` int NOT NULL,
        `tables` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';



CREATE TABLE `template_design` (
        `id` bigint NOT NULL AUTO_INCREMENT,
        `setting_name` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '配置名称',
        `remark` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '说明',
        `setting_type` int NOT NULL default '0' COMMENT '类型',
        `config` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='模板设计表';

alter table template add  column `design_id` int NOT NULL DEFAULT '0' COMMENT '模板设计表id';

alter table template_design add  column `create_time` bigint NOT NULL DEFAULT '0' COMMENT '创建时间';
alter table template_design add  column  `update_time` bigint NOT NULL DEFAULT '0' COMMENT '修改时间';
alter table template_design add  column `creater` int unsigned NOT NULL DEFAULT '0' COMMENT '创建者';

-- 2025-02-13
alter table template_design add  column  `image` varchar(512) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '图片';

-- 2025-03-20 增加新闻是否自动发布字段
ALTER TABLE `gtcms`.`news` ADD COLUMN `is_hot` tinyint NOT NULL COMMENT '是否自动（1是0否）' AFTER `is_hot`;

-- 2025-03-23 站点添加直播产品设置
ALTER TABLE `gtcms`.`site`
ADD COLUMN `video_products_show_pc` tinyint NOT NULL DEFAULT 1 COMMENT '直播产品是否显示pc端(1:是 2:否)',
ADD COLUMN `video_products_show_mobile` tinyint NOT NULL DEFAULT 1 COMMENT '直播产品是否显示移动端(1:是 2:否)' AFTER `video_products_show_pc`,
ADD COLUMN `open_referer` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)' AFTER `video_products_show_mobile`,
ADD COLUMN `open_video_products_ctrl` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)' AFTER `open_referer`,
ADD COLUMN `video_products_ctrl_sec` int NOT NULL DEFAULT 0 COMMENT '开赛前多少分钟' AFTER `open_video_products_ctrl`,
ADD COLUMN `video_products_ctrl_min` int NOT NULL DEFAULT 0 COMMENT '开赛前多少分钟' AFTER `video_products_ctrl_sec`,
ADD COLUMN `public_welfare_show` tinyint NOT NULL DEFAULT 0 COMMENT '是否展示公益信号' AFTER `video_products_ctrl_min`,
ADD COLUMN `video_products_ctrl_min_end` int NOT NULL COMMENT '开赛后多少分钟' AFTER `public_welfare_show`,
ADD COLUMN `open_signal_domain` tinyint NOT NULL DEFAULT 0 COMMENT '开启信号域名跳转(1是 2否)' AFTER `video_products_ctrl_min_end`,
ADD COLUMN `video_products_show_setting` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '直播产品显示设置' AFTER `open_signal_domain`,
ADD COLUMN `open_referer_mobile` tinyint NOT NULL COMMENT '是否开启搜索来路校验(1:是 2:否)' AFTER `video_products_show_setting`,
ADD COLUMN `video_products_ctrl_sec_mobile` int NOT NULL DEFAULT 0 COMMENT '开赛前多少分钟' AFTER `open_referer_mobile`,
ADD COLUMN `open_video_products_ctrl_mobile` tinyint NOT NULL COMMENT '是否开启直播产品控制(1:是 2:否)' AFTER `video_products_ctrl_sec_mobile`,
ADD COLUMN `video_products_ctrl_min_mobile` int NOT NULL DEFAULT 0 COMMENT '开赛前多少分钟' AFTER `open_video_products_ctrl_mobile`,
ADD COLUMN `public_welfare_show_mobile` tinyint NOT NULL DEFAULT 0 COMMENT '是否展示公益信号' AFTER `video_products_ctrl_min_mobile`,
ADD COLUMN `video_products_ctrl_min_end_mobile` int NOT NULL COMMENT '开赛后多少分钟' AFTER `public_welfare_show_mobile`,
ADD COLUMN `open_signal_domain_mobile` tinyint NOT NULL DEFAULT 0 COMMENT '开启信号域名跳转(1是 2否)' AFTER `video_products_ctrl_min_end_mobile`,
ADD COLUMN `video_products_show_setting_mobile` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '直播产品显示设置' AFTER `open_signal_domain_mobile`,
ADD COLUMN `video_products` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '直播源产品列表' AFTER `video_products_show_setting_mobile`,
ADD COLUMN `use_products` tinyint NOT NULL COMMENT '是否使用直播产品（1是 0否）' AFTER `video_products`;

-- 2025-05-02 加入ai改造文章
ALTER TABLE `gtcms`.`news`
    ADD COLUMN `is_ai` tinyint NOT NULL COMMENT '是否ai生成（1是0否）' AFTER `is_auto`;

ALTER TABLE `gtcms`.`crawler_news_raw`
    MODIFY COLUMN `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文章正文' AFTER `update_time`;

ALTER TABLE `gtcms`.`crawler_news_raw`
    ADD COLUMN `is_ai` tinyint NOT NULL COMMENT '是否ai(1是0否)' AFTER `is_video`;

ALTER TABLE `crawler_news_raw` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

--2025-05-08
ALTER TABLE `gtcms`.`site_group`
    ADD COLUMN `keyword` varchar(255) NOT NULL DEFAULT '' COMMENT '关键词(用于文章)' AFTER `model_fake_bit`;


--2025-05-26
ALTER TABLE signal_click_record
    ADD UNIQUE KEY uniq_product_day (product_id, record_day);




-- 斯诺克表
-- https://open.sportnanoapi.com/api/v1/snooker/match/list
--
CREATE TABLE `collect_snooker_match` (
                                         `id` bigint NOT NULL COMMENT '比赛id',
                                         `season_id` int NOT NULL DEFAULT '0' COMMENT '赛季id',
                                         `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
                                         `tournament_id` int NOT NULL DEFAULT '0' COMMENT '阶段id',
                                         `home_team_id` int NOT NULL DEFAULT '0' COMMENT '主队id',
                                         `away_team_id` int NOT NULL DEFAULT '0' COMMENT '客队id',
                                         `status_id` tinyint NOT NULL DEFAULT '0' COMMENT '状态id, 见文档 ',
                                         `match_time` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '比赛时间， yyyy-mm-dd HH:MM:SS',
                                         `match_ts` bigint NOT NULL DEFAULT '0' COMMENT '比赛时间的时间戳',
                                         `venue_id` int NOT NULL DEFAULT '0' COMMENT '场馆id',
                                         `neutral` tinyint NOT NULL DEFAULT '0' COMMENT '是否中立场，1-是、0-否',
                                         `bestof` tinyint NOT NULL DEFAULT '0' COMMENT '盘数，0-未知',
                                         `tbd` tinyint NOT NULL DEFAULT '0' COMMENT '比赛时间是否待定 1.是',
                                         `scores` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比分数据（可能为空）',
                                         `home_scores` int NOT NULL DEFAULT '0' COMMENT '客队id',
                                         `away_scores` int NOT NULL DEFAULT '0' COMMENT '客队id',
                                         `match_ids` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '团体赛下关联的比赛',
                                         `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
                                         `title` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '我们自己加的标题',
                                         PRIMARY KEY (`id`),
                                         KEY `away_id` (`away_team_id`),
                                         KEY `home_id` (`home_team_id`),
                                         KEY `match_ts_index` (`match_ts`),
                                         KEY `competition_id` (`competition_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='比赛数据';


-- 获取赛程赛果列表-日期查询
-- https://open.sportnanoapi.com/api/v1/snooker/match/diary
--
-- CREATE TABLE `collect_snooker_competition_stats` (
--          `id` int NOT NULL COMMENT '赛事id',
--          `season_id` int NOT NULL,
--          `match` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '比赛列表',
--          `unique_tournament` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事列表',
--          `team` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '球队列表',
--          PRIMARY KEY (`id`)
-- ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事统计';
--



-- https://open.sportnanoapi.com/api/v1/snooker/unique_tournament/list
CREATE TABLE `collect_snooker_competition` (
                                               `id` int NOT NULL,
                                               `category_id` int NOT NULL DEFAULT '0' COMMENT '分类id',
                                               `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
                                               `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
                                               `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

                                               `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
                                               `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
                                               `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
                                               `uid` int NOT NULL DEFAULT '0' COMMENT '赛事id(重复赛事合并后的对应id)',
                                               `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '赛事logo',
                                               `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
                                               PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛事表';


-- https://open.sportnanoapi.com/api/v1/snooker/season/list
CREATE TABLE `collect_snooker_season` (
                                          `id` int NOT NULL,
                                          `competition_id` int NOT NULL DEFAULT '0' COMMENT '赛事id',
                                          `year` varchar(10) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '赛季年份',
                                          `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
                                          `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
                                          `is_current` int NOT NULL DEFAULT '0' COMMENT '是否最新赛季，1-是、0-否',
                                          `updated_at` bigint NOT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='赛季';



-- https://open.sportnanoapi.com/api/v1/snooker/team/list

CREATE TABLE `collect_snooker_team` (
                                        `id` int NOT NULL,
                                        `name_zh` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文名称',
                                        `name_zht` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语名称',
                                        `name_en` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文名称',

                                        `short_name_zh` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '中文简称',
                                        `short_name_zht` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '粤语简称',
                                        `short_name_en` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '英文简称',
                                        `abbr` varchar(80) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL DEFAULT '' COMMENT '缩写',

                                        `logo` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT 'logo',
                                        `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别，1-男、2-女、0-未知',
                                        `national` tinyint NOT NULL DEFAULT '0' COMMENT '是否国家队，1-是、0-否',
                                        `uid` tinyint NOT NULL DEFAULT '0' COMMENT '球队id(重复球队合并后的对应id)',

                                        `country_id` int NOT NULL DEFAULT '0' COMMENT '国家id',
                                        `extra` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL COMMENT '个人统计数据（没有数据，字段不存在）',
                                        `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
                                        `create_time` bigint NOT NULL,
                                        `update_time` bigint NOT NULL,
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';


-- 获取球队统计列表
-- https://open.sportnanoapi.com/api/v1/snooker/team/stats/list

CREATE TABLE `collect_snooker_team_stats` (
                                              `id` int NOT NULL,
                                              `team_id` int NOT NULL DEFAULT '0' COMMENT '球队id',
                                              `played` int NOT NULL DEFAULT '0' COMMENT '总局数',
                                              `won` int NOT NULL DEFAULT '0' COMMENT '胜局数',
                                              `lost` int NOT NULL DEFAULT '0' COMMENT '负局数',
                                              `won_rate` FLOAT(10,2) NOT NULL DEFAULT 0 COMMENT '胜率',
        `scores` int NOT NULL DEFAULT '0' COMMENT '总得分',
        `scores_per` FLOAT(10,2) NOT NULL DEFAULT 0 COMMENT '每局平均得分',

        `over_fifty` int NOT NULL DEFAULT '0' COMMENT '单杆超过50分次数',
        `over_fifty_rate` FLOAT(10,2) NOT NULL DEFAULT 0  COMMENT '单杆超过50分基数',
        `over_hundred` int NOT NULL DEFAULT '0' COMMENT '单杆超过100分次数',
        `over_hundred_rate` FLOAT(10,2) NOT NULL DEFAULT 0  COMMENT '单杆超过100分基数',

        `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
        `create_time` bigint NOT NULL,
        `update_time` bigint NOT NULL,
        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队信息，单个球员，球队id可看为球员id';




-- 获取赛程赛果列表-日期查询
-- https://open.sportnanoapi.com/api/v1/snooker/team/ranking

CREATE TABLE `collect_snooker_team_ranking` (
                                                `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                `team_id` int NOT NULL DEFAULT '0' COMMENT '球队id',
                                                `type_id` int NOT NULL DEFAULT '0' COMMENT '榜单类型，1-世界排名、2-BETVICTOR 系列赛排名、3-CAZOO 系列赛排名、4-近1年排名',
                                                `ranking` int NOT NULL DEFAULT '0' COMMENT '名次',
                                                `price` int NOT NULL DEFAULT '0' COMMENT '奖金',
                                                `price_mode` int NOT NULL DEFAULT '0' COMMENT '奖金单位，1-英镑£',
                                                `updated_at` bigint NOT NULL DEFAULT '0' COMMENT '更新时间',
                                                `create_time` bigint NOT NULL,
                                                `update_time` bigint NOT NULL,
                                                PRIMARY KEY (`id`),
                                                UNIQUE KEY `team_rank_pk2` (`team_id`,`type_id`,`ranking`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='球队排行榜';


CREATE TABLE `collect_snooker_match_live` (
                                              `id` int NOT NULL COMMENT '赛事id',
                                              `score` text CHARACTER SET utf8mb3 COLLATE utf8mb3_bin NOT NULL,
                                              `status` int NOT NULL DEFAULT '0' COMMENT '赛事状态',
                                              `seconds` int NOT NULL COMMENT '小节剩余时间(秒)',
                                              PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_bin COMMENT='snooker实时统计';




--在测试服务器同步
-- create table if not exists quran_juz(
--                                         id                       int unsigned auto_increment primary key,
--                                         numbers                  int unsigned default '0' not null comment 'juz章节数 (1-30)',
--                                         surahs                  int unsigned default '0' not null comment 'surahs数',
--                                         ayahs                  int unsigned default '0' not null comment 'ayahs数',
--                                         first_surah                  int unsigned default '0' not null comment '第一surahs数',
--                                         last_surah                  int unsigned default '0' not null comment '最后surahs数'
-- )comment '古兰经-juz对照表 只有30条数据' collate = utf8mb4_general_ci;
--


--juz 需要多级数据返回
--章节 二级下拉
--维护每个surah 在哪一个juz

create table if not exists surah_read_record(
    id                       int unsigned auto_increment primary key,
    user_id                  int unsigned default '0' not null comment '用户id',
    surah_id                  int unsigned default '0' not null comment 'surah章节id',
    surah_name                  int unsigned default '0' not null comment 'surah章节名',
    is_user_op                  int unsigned default '0' not null comment '是否用户操作，1是 0否',
    create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
    update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '用户阅读记录表' collate = utf8mb4_general_ci;


create table if not exists surah_read_collect(
        id                       int unsigned auto_increment primary key,
        user_id                  int unsigned default '0' not null comment '用户id',
        surah_id                  int unsigned default '0' not null comment 'surah章节id',
        surah_name                  int unsigned default '0' not null comment 'surah章节名',
        create_time              bigint       default 0  default '0' not null comment '创建时间（注册时间）',
        update_time              bigint       default 0  default '0'  not null comment '更新时间，0代表创建后未更新'
)comment '用户收藏表' collate = utf8mb4_general_ci;



create table if not exists user
(
    id                       int unsigned auto_increment
    primary key,
    agent_id                 int unsigned default '0' not null comment '上级（代理）的id',
    agent_code               varchar(32)  default ''  not null comment '注册时填写的代理码（选填）',
    account                  varchar(32)  default ''  not null comment '账号',
    password                 varchar(64)  default ''  not null comment '密码',
    pay_password             varchar(64)  default ''  not null comment '支付密码',
    password_modify_time     bigint       default 0   not null comment '登录密码支付密码最近修改时间',
    area_code                char(6)      default ''  not null comment '手机国际区号，如：86',
    phone_num                char(16)     default ''  not null comment '手机号',
    bind_phone_time          bigint       default 0   not null comment '手机号绑定时间',
    email                    varchar(64)  default ''  not null comment '邮箱地址',
    bind_email_time          bigint       default 0   not null comment '邮箱绑定时间',
    bind_real_name_time      bigint       default 0   not null comment '真实姓名绑定时间',
    vip_level                int          default 0   not null comment 'vip等级',
    level_id                 int unsigned default '0' not null comment '会员层级id',
    is_banned                tinyint      default 1   not null comment '账号封号状态： 1 正常 2 封号',
    is_prohibit              tinyint      default 1   not null comment '提取状态：1 正常 2 禁提',
    is_online                tinyint      default 2   not null comment '是否在线：1是  2 否',
    online_duration          bigint       default 0   not null comment '在线时长（单位：秒）',
    signin_count             int          default 0   not null comment '登录次数',
    last_signin_time         bigint       default 0   not null comment '最后一次登录时间',
    last_signin_ip           varchar(64)  default ''  not null comment '最后登录ip',
    last_signin_device_id    varchar(64)  default ''  not null comment '最后登录设备号',
    last_signin_app_type     tinyint      default 0   not null comment '最近登录应用类型（1:android  2: ios，3:h5，4:web，5:其他）',
    last_signin_app_version  varchar(32)  default ''  not null comment '最近登录应用类型版本号',
    signup_ip                varchar(64)  default ''  not null comment '注册ip',
    signup_ip_region         varchar(64)  default ''  not null comment '注册IP地理区域',
    signup_device_id         varchar(64)  default ''  not null comment '注册设备号（设备指纹）',
    signup_device_os         varchar(32)  default ''  not null comment '注册设备系统（android,ios,windows,mac,...）',
    signup_device_os_version varchar(32)  default ''  not null comment '注册设备系统版本号',
    signup_device_type       tinyint      default 0   not null comment '注册设备类型（1:mobile手机，2:desktop台式，3:pad平板，。。。其他）',
    signup_app_type          tinyint      default 0   not null comment '应用类型（1:android  2: ios，3:h5，4:web，5:其他）',
    signup_app_version       varchar(32)  default ''  not null comment '注册应用类型版本号',
    signup_host              varchar(64)  default ''  not null comment '注册域名(接口域名)',
    signup_domain            varchar(64)  default ''  not null comment '注册域名(页面原始域名)',
    signup_domain2           varchar(64)  default ''  not null comment '注册域名(页面域名)',
    last_signin_log_id       int          default 0   not null comment '最后一次登录日志id（如果有分表的话，要考虑时间）',
    device_token_ios         varchar(256) default ''  not null comment 'IOS推送token',
    device_token_android     varchar(256) default ''  not null comment 'android推送token(FCM)',
    security_password        varchar(64)  default ''  not null comment '安全密码，修改个人绑定信息时要验证',
    version                  int          default 1   null comment '该记录的版本号',
    is_test                  int          default 0   not null comment '测试账号：  1 是 ，其他值：否',
    limit_start_time         bigint       default 0   not null comment '限制登录开始时间',
    limit_end_time           bigint       default 0   not null comment '限制登录结束时间',
    create_time              bigint       default 0   not null comment '创建时间（注册时间）',
    update_time              bigint       default 0   not null comment '更新时间，0代表创建后未被修改过（哪些字段的更新会触发这个？）',
    create_account           varchar(32)  default ''  not null comment '创建者账号',
    create_type              tinyint      default 0   not null comment '创建者来源(注册来源)（1会员注册user，2代理添加agent，3管理员添加admin）',
    update_account           varchar(32)  default ''  not null comment '更新者账号',
    update_type              tinyint      default 0   not null comment '更新者来源',
    invite_code              varchar(32)  default ''  not null comment '邀请码',
    transfer_code            varchar(16)  default ''  not null comment '转线码',
    noob_task_finish_time    bigint       default 0   not null comment '新手任务完成时间',
    data_type                tinyint      default 1   not null comment '数据类型:1正式数据;2测试数据',
    pixel_id                 varchar(255) default ''  not null comment '像素id',
    source                   tinyint      default 0   not null comment '注册来源( 1直客，2代理，3邀请，4后台）',
    constraint account_idx
    unique (account) comment '账号'
    )
    comment '会员表（部分可根据需要考虑分表出来）' collate = utf8mb4_general_ci;
