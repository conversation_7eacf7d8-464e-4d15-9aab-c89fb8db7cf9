package cmsLoginConfig

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/util/gconv"
	v1 "gtcms/api/v1"
	"gtcms/internal/dao"
	"gtcms/internal/model"
	"gtcms/internal/model/entity"
	"gtcms/internal/service"
	"io"
	"log"
	"net/http"
	"sort"
	"time"
)

type (
	sCmsLoginConfig struct{}
)

func init() {
	service.RegisterCmsLoginConfig(New())
}

var cl = dao.CmsLoginConfig.Columns()

func New() service.ICmsLoginConfig {
	return &sCmsLoginConfig{}
}

func (s *sCmsLoginConfig) Edit(ctx context.Context, id uint, in *entity.CmsLoginConfig) (err error) {
	var oldData entity.CmsLoginConfig
	_ = dao.CmsLoginConfig.Ctx(ctx).WherePri(id).Scan(&oldData)

	admin, _ := service.Utility().GetSelf(ctx)
	in.UpdateAccount = admin.Account
	in.UpdateTime = time.Now().UnixMilli()

	_, err = dao.CmsLoginConfig.Ctx(ctx).WherePri(id).Where(cl.DeleteTime, 0).Data(in).Update()
	if err == nil {
		_ = service.AccountNodeConfig().PushLog(ctx, &model.AdminModifyInput{
			OldRecordOrAttr: oldData,
			NewRecordOrAttr: in,
		})
	}

	return
}

// Detail 查询登录配置详情
func (s *sCmsLoginConfig) Detail(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {
	err = dao.CmsLoginConfig.Ctx(ctx).Limit(1).Scan(&out)
	if out == nil {
		return nil, gerror.New("None config")
	}
	return
}

// DetailCache 查询登录配置详情(有缓存)
func (s *sCmsLoginConfig) DetailCache(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {
	err = dao.CmsLoginConfig.Ctx(ctx).Limit(1).Scan(&out)
	if out == nil {
		return nil, gerror.New("None config")
	}
	return
}
func (s *sCmsLoginConfig) SynQuranJuz(ctx context.Context) (out *v1.CmsLoginConfigDetailRes, err error) {

	//return
	for i := 1; i <= 30; i++ {
		// ------------- 主函数 -------------
		url := "https://api.alquran.cloud/v1/juz/" + gconv.String(i) + "/en.asad" // 替换成真实地址
		// 1. 构造客户端（带 10 秒超时）
		client := http.Client{Timeout: 10 * time.Second}

		// 2. 发起 GET 请求
		resp, err := client.Get(url)
		if err != nil {
			log.Fatalf("request failed: %v", err)
		}
		defer resp.Body.Close()

		// 3. 读取响应体
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			log.Fatalf("read body failed: %v", err)
		}

		// 4. 反序列化
		var data v1.Response
		if err := json.Unmarshal(body, &data); err != nil {
			log.Fatalf("unmarshal failed: %v", err)
		}
		//print(data.Data)
		//for _, ayah := range data.Data {

		// 1. 取出所有键
		keys := make([]int, 0, len(data.Data.Surahs))
		for k := range data.Data.Surahs {
			keys = append(keys, k)
		}

		// 2. 排序
		sort.Ints(keys)

		// 3. 取最小键（第一个）
		firstKey := keys[0]
		// 4. 取最大键（最后一个）
		lastKey := keys[len(keys)-1]
		dataInsert := &entity.QuranJuz{
			Numbers:    uint(data.Data.Number),
			Ayahs:      uint(len(data.Data.Ayahs)),
			Surahs:     uint(len(data.Data.Surahs)),
			FirstSurah: uint(firstKey),
			LastSurah:  uint(lastKey),
		}
		_, err = dao.QuranJuz.Ctx(ctx).Insert(&dataInsert)
		if err == nil {
			return nil, gerror.New("Error")
		}
		time.Sleep(5 * time.Second)
	}

	return
}
