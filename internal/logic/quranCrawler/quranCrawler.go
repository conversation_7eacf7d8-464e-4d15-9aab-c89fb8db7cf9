package quranCrawler

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"

	v1 "gtcms/api/v1"
	islamic_dao "gtcms/internal/dao/islamic_content_svc/dao"
	islamic_do "gtcms/internal/dao/islamic_content_svc/model/do"
	islamic_entity "gtcms/internal/dao/islamic_content_svc/model/entity"
	"gtcms/internal/service"
)

// sQuranCrawler 古兰经爬虫服务实现
type sQuranCrawler struct {
	crawlMutex sync.Mutex
	isRunning  bool
	progress   *CrawlProgress
}

// CrawlProgress 爬取进度
type CrawlProgress struct {
	IsRunning    bool      `json:"isRunning"`
	CurrentSurat int       `json:"currentSurat"`
	CurrentStep  string    `json:"currentStep"`
	StartTime    time.Time `json:"startTime"`
	ErrorCount   int       `json:"errorCount"`
	LastError    string    `json:"lastError"`
}

// API响应结构体
type SuratListResponse []*SuratItem

type SuratItem struct {
	Nomor       int    `json:"nomor"`
	Nama        string `json:"nama"`
	NamaLatin   string `json:"nama_latin"`
	JumlahAyat  int    `json:"jumlah_ayat"`
	TempatTurun string `json:"tempat_turun"`
	Arti        string `json:"arti"`
	Deskripsi   string `json:"deskripsi"`
	Audio       string `json:"audio"`
}

type SuratDetailResponse struct {
	Nomor       int         `json:"nomor"`
	Nama        string      `json:"nama"`
	NamaLatin   string      `json:"nama_latin"`
	JumlahAyat  int         `json:"jumlah_ayat"`
	TempatTurun string      `json:"tempat_turun"`
	Arti        string      `json:"arti"`
	Deskripsi   string      `json:"deskripsi"`
	Audio       string      `json:"audio"`
	Ayat        []*AyatItem `json:"ayat"`
	Status      bool        `json:"status"`
}

type AyatItem struct {
	Id    int    `json:"id"`
	Surah int    `json:"surah"`
	Nomor int    `json:"nomor"`
	Ar    string `json:"ar"`
	Tr    string `json:"tr"`
	Idn   string `json:"idn"`
}

type TafsirResponse struct {
	Status      bool          `json:"status"`
	Nomor       int           `json:"nomor"`
	Nama        string        `json:"nama"`
	NamaLatin   string        `json:"nama_latin"`
	JumlahAyat  int           `json:"jumlah_ayat"`
	TempatTurun string        `json:"tempat_turun"`
	Arti        string        `json:"arti"`
	Deskripsi   string        `json:"deskripsi"`
	Audio       string        `json:"audio"`
	Tafsir      []*TafsirItem `json:"tafsir"`
}

type TafsirItem struct {
	Id     int    `json:"id"`
	Surah  int    `json:"surah"`
	Ayat   int    `json:"ayat"`
	Tafsir string `json:"tafsir"`
}

const (
	BaseURL       = "https://equran.id/api"
	StepSuratList = "surat_list"
	StepAyat      = "ayat"
	StepTafsir    = "tafsir"
)

func init() {
	service.RegisterQuranCrawler(New())
}

// New 创建古兰经爬虫服务实例
func New() service.IQuranCrawler {
	return &sQuranCrawler{
		progress: &CrawlProgress{},
	}
}

// Start 开始爬取古兰经数据
func (s *sQuranCrawler) Start(_ctx context.Context, req *v1.QuranCrawlerStartReq) (res *v1.QuranCrawlerStartRes, err error) {
	s.crawlMutex.Lock()

	if s.isRunning {
		s.crawlMutex.Unlock()
		return &v1.QuranCrawlerStartRes{
			Success: false,
			Message: "爬取任务正在进行中，请稍后再试",
		}, nil
	}

	// 设置运行状态
	s.isRunning = true
	s.progress = &CrawlProgress{
		IsRunning:   true,
		StartTime:   time.Now(),
		CurrentStep: StepSuratList,
	}
	s.crawlMutex.Unlock()

	// 启动异步爬取任务
	go s.runCrawlTask(req.Force)

	// 立即返回响应
	return &v1.QuranCrawlerStartRes{
		Success: true,
		Message: "爬取任务已启动，正在后台运行",
	}, nil
}

// runCrawlTask 异步执行爬取任务
func (s *sQuranCrawler) runCrawlTask(force bool) {
	ctx := context.Background()

	defer func() {
		s.crawlMutex.Lock()
		s.isRunning = false
		s.progress.IsRunning = false
		s.crawlMutex.Unlock()
	}()

	startTime := time.Now()

	// 统计变量
	var totalSurat, crawledSurat, totalAyat, crawledAyat, totalTafsir, crawledTafsir int

	// 1. 爬取章节列表
	g.Log().Info(ctx, "开始爬取古兰经章节列表...")
	suratList, err := s.fetchSuratList(ctx)
	if err != nil {
		s.progress.LastError = err.Error()
		s.progress.ErrorCount++
		g.Log().Errorf(ctx, "获取章节列表失败: %v", err)
		return
	}

	totalSurat = len(suratList)
	g.Log().Infof(ctx, "获取到 %d 个章节", totalSurat)

	// 2. 保存章节列表到数据库
	for _, surat := range suratList {
		s.progress.CurrentSurat = surat.Nomor

		err = s.saveSurat(ctx, surat, force)
		if err != nil {
			g.Log().Errorf(ctx, "保存章节 %d 失败: %v", surat.Nomor, err)
			s.progress.ErrorCount++
			continue
		}
		crawledSurat++
	}

	// 3. 爬取每个章节的经文和注释
	for _, surat := range suratList {
		s.progress.CurrentSurat = surat.Nomor

		// 爬取经文
		s.progress.CurrentStep = StepAyat

		ayatCount, err := s.crawlSuratAyat(ctx, surat.Nomor, force)
		if err != nil {
			g.Log().Errorf(ctx, "爬取章节 %d 经文失败: %v", surat.Nomor, err)
			s.progress.ErrorCount++
			s.progress.LastError = err.Error()
		} else {
			crawledAyat += ayatCount
			totalAyat += surat.JumlahAyat
		}

		// 爬取注释
		s.progress.CurrentStep = StepTafsir

		tafsirCount, err := s.crawlSuratTafsir(ctx, surat.Nomor, force)
		if err != nil {
			g.Log().Errorf(ctx, "爬取章节 %d 注释失败: %v", surat.Nomor, err)
			s.progress.ErrorCount++
			s.progress.LastError = err.Error()
		} else {
			crawledTafsir += tafsirCount
			totalTafsir += surat.JumlahAyat
		}
	}

	duration := time.Since(startTime)

	g.Log().Infof(ctx, "古兰经数据爬取完成，耗时: %v，统计: 章节%d/%d，经文%d/%d，注释%d/%d",
		duration, crawledSurat, totalSurat, crawledAyat, totalAyat, crawledTafsir, totalTafsir)
}

// fetchSuratList 获取章节列表
func (s *sQuranCrawler) fetchSuratList(ctx context.Context) ([]*SuratItem, error) {
	client := gclient.New()
	client.SetTimeout(30 * time.Second)

	resp, err := client.Get(ctx, BaseURL+"/surat")
	if err != nil {
		return nil, gerror.Wrap(err, "请求章节列表API失败")
	}
	defer resp.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, gerror.Newf("API返回错误状态码: %d", resp.StatusCode)
	}

	var suratList []*SuratItem
	err = json.Unmarshal(resp.ReadAll(), &suratList)
	if err != nil {
		return nil, gerror.Wrap(err, "解析章节列表响应失败")
	}

	return suratList, nil
}

// saveSurat 保存章节信息
func (s *sQuranCrawler) saveSurat(ctx context.Context, surat *SuratItem, force bool) error {
	// 检查是否已存在
	if !force {
		count, err := islamic_dao.SuratDaftar.Ctx(ctx).Where("nomor", surat.Nomor).Count()
		if err != nil {
			return gerror.Wrap(err, "检查章节是否存在失败")
		}
		if count > 0 {
			return nil // 已存在，跳过
		}
	}

	// 保存或更新章节信息
	_, err := islamic_dao.SuratDaftar.Ctx(ctx).Data(islamic_do.SuratDaftar{
		Nomor:       surat.Nomor,
		Nama:        surat.Nama,
		NamaLatin:   surat.NamaLatin,
		JumlahAyat:  surat.JumlahAyat,
		TempatTurun: surat.TempatTurun,
		Arti:        surat.Arti,
		Deskripsi:   surat.Deskripsi,
		Audio:       surat.Audio,
		Status:      1,
	}).OnDuplicate("nama,nama_latin,jumlah_ayat,tempat_turun,arti,deskripsi,audio,updated_at").Save()

	return err
}

// crawlSuratAyat 爬取章节经文
func (s *sQuranCrawler) crawlSuratAyat(ctx context.Context, nomor int, force bool) (int, error) {
	// 检查是否已存在
	if !force {
		count, err := islamic_dao.SuratAyat.Ctx(ctx).Where("surah_id", nomor).Count()
		if err != nil {
			return 0, gerror.Wrap(err, "检查经文是否存在失败")
		}
		if count > 0 {
			return int(count), nil // 已存在，返回数量
		}
	}

	// 获取章节详情
	client := gclient.New()
	client.SetTimeout(30 * time.Second)

	resp, err := client.Get(ctx, fmt.Sprintf("%s/surat/%d", BaseURL, nomor))
	if err != nil {
		return 0, gerror.Wrap(err, "请求章节详情API失败")
	}
	defer resp.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, gerror.Newf("API返回错误状态码: %d", resp.StatusCode)
	}

	var suratDetail SuratDetailResponse
	err = json.Unmarshal(resp.ReadAll(), &suratDetail)
	if err != nil {
		return 0, gerror.Wrap(err, "解析章节详情响应失败")
	}

	// 保存经文
	crawledCount := 0
	for _, ayat := range suratDetail.Ayat {
		_, err = islamic_dao.SuratAyat.Ctx(ctx).Data(islamic_do.SuratAyat{
			AyatId:  ayat.Id,
			SurahId: ayat.Surah,
			Nomor:   ayat.Nomor,
			Ar:      ayat.Ar,
			Tr:      ayat.Tr,
			Idn:     ayat.Idn,
		}).OnDuplicate("ayat_id,ar,tr,idn,updated_at").Save()

		if err != nil {
			g.Log().Errorf(ctx, "保存经文失败 - 章节:%d, 经文:%d, 错误:%v", nomor, ayat.Nomor, err)
			continue
		}
		crawledCount++
	}

	return crawledCount, nil
}

// crawlSuratTafsir 爬取章节"注释"
func (s *sQuranCrawler) crawlSuratTafsir(ctx context.Context, nomor int, force bool) (int, error) {
	// 检查是否已存在
	if !force {
		count, err := islamic_dao.SuratTafsir.Ctx(ctx).Where("surah_id", nomor).Count()
		if err != nil {
			return 0, gerror.Wrap(err, "检查注释是否存在失败")
		}
		if count > 0 {
			return int(count), nil // 已存在，返回数量
		}
	}

	// 获取"注释"
	client := gclient.New()
	client.SetTimeout(30 * time.Second)

	resp, err := client.Get(ctx, fmt.Sprintf("%s/tafsir/%d", BaseURL, nomor))
	if err != nil {
		return 0, gerror.Wrap(err, "请求注释API失败")
	}
	defer resp.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, gerror.Newf("API返回错误状态码: %d", resp.StatusCode)
	}

	var tafsirResp TafsirResponse
	err = json.Unmarshal(resp.ReadAll(), &tafsirResp)
	if err != nil {
		return 0, gerror.Wrap(err, "解析注释响应失败")
	}

	// 保存"注释"
	crawledCount := 0
	for _, tafsir := range tafsirResp.Tafsir {
		_, err = islamic_dao.SuratTafsir.Ctx(ctx).Data(islamic_do.SuratTafsir{
			TafsirId:  tafsir.Id,
			SurahId:   tafsir.Surah,
			AyatNomor: tafsir.Ayat,
			Tafsir:    tafsir.Tafsir,
		}).OnDuplicate("tafsir_id,tafsir,updated_at").Save()

		if err != nil {
			g.Log().Errorf(ctx, "保存注释失败 - 章节:%d, 经文:%d, 错误:%v", nomor, tafsir.Ayat, err)
			continue
		}
		crawledCount++
	}

	return crawledCount, nil
}

// Status 查询古兰经数据状态
func (s *sQuranCrawler) Status(ctx context.Context, req *v1.QuranCrawlerStatusReq) (res *v1.QuranCrawlerStatusRes, err error) {
	// 获取总体统计
	totalSurat, err := islamic_dao.SuratDaftar.Ctx(ctx).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节总数失败")
	}

	totalAyat, err := islamic_dao.SuratAyat.Ctx(ctx).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取经文总数失败")
	}

	totalTafsir, err := islamic_dao.SuratTafsir.Ctx(ctx).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取注释总数失败")
	}

	// 计算古兰经总经文数
	expectedTotalAyat, err := islamic_dao.SuratDaftar.Ctx(ctx).Sum(`jumlah_ayat`)
	if err != nil {
		return nil, gerror.Wrap(err, "计算古兰经总经文数失败")
	}

	// 获取所有章节
	var allSurat []islamic_entity.SuratDaftar
	err = islamic_dao.SuratDaftar.Ctx(ctx).Scan(&allSurat)
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节列表失败")
	}

	// 获取完整章节数（经文和注释都完整的章节）
	// 检查每个章节的完整性
	// 简化查询，先获取所有章节，然后逐个检查完整性。数据量不多，先这样实现，后续有需要再优化
	var completedSurat int64 = 0
	for _, surat := range allSurat {
		ayatCount, _ := islamic_dao.SuratAyat.Ctx(ctx).Where("surah_id", surat.Nomor).Count()
		tafsirCount, _ := islamic_dao.SuratTafsir.Ctx(ctx).Where("surah_id", surat.Nomor).Count()

		if int(ayatCount) == surat.JumlahAyat && int(tafsirCount) == surat.JumlahAyat {
			completedSurat++
		}
	}

	// 获取章节状态详情
	suratStatus, err := s.getSuratStatusList(ctx)
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节状态失败")
	}

	// 获取爬取进度
	crawlProgress := &v1.QuranCrawlProgress{
		IsRunning:    s.progress.IsRunning,
		CurrentSurat: s.progress.CurrentSurat,
		CurrentStep:  s.progress.CurrentStep,
		ErrorCount:   s.progress.ErrorCount,
		LastError:    s.progress.LastError,
	}

	if s.progress.IsRunning {
		crawlProgress.StartTime = s.progress.StartTime.Format("2006-01-02 15:04:05")
	}

	res = &v1.QuranCrawlerStatusRes{
		TotalSurat:     int(totalSurat),
		CompletedSurat: int(completedSurat),
		TotalAyat:      int(expectedTotalAyat), // 古兰经总经文数
		CrawledAyat:    int(totalAyat),
		TotalTafsir:    int(expectedTotalAyat), // 古兰经总注释数（与经文数相同）
		CrawledTafsir:  int(totalTafsir),
		SuratStatus:    suratStatus,
		CrawlProgress:  crawlProgress,
	}

	return res, nil
}

// getSuratStatusList 获取章节状态列表
func (s *sQuranCrawler) getSuratStatusList(ctx context.Context) ([]*v1.QuranSuratStatus, error) {
	var suratList []islamic_entity.SuratDaftar
	err := islamic_dao.SuratDaftar.Ctx(ctx).OrderAsc("nomor").Scan(&suratList)
	if err != nil {
		return nil, err
	}

	var statusList []*v1.QuranSuratStatus
	for _, surat := range suratList {
		// 获取经文数量
		ayatCount, _ := islamic_dao.SuratAyat.Ctx(ctx).Where("surah_id", surat.Nomor).Count()

		// 获取注释数量
		tafsirCount, _ := islamic_dao.SuratTafsir.Ctx(ctx).Where("surah_id", surat.Nomor).Count()

		statusList = append(statusList, &v1.QuranSuratStatus{
			Nomor:         surat.Nomor,
			NamaLatin:     surat.NamaLatin,
			JumlahAyat:    surat.JumlahAyat,
			CrawledAyat:   int(ayatCount),
			CrawledTafsir: int(tafsirCount),
			LastUpdate:    surat.UpdatedAt.Format("Y-m-d H:i:s"),
		})
	}

	return statusList, nil
}

// SuratList 获取章节列表
func (s *sQuranCrawler) SuratList(ctx context.Context, req *v1.QuranCrawlerSuratListReq) (res *v1.QuranCrawlerSuratListRes, err error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Size <= 0 {
		req.Size = 20
	}

	// 获取总数
	total, err := islamic_dao.SuratDaftar.Ctx(ctx).Count()
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节总数失败")
	}

	// 获取列表
	var suratList []islamic_entity.SuratDaftar
	err = islamic_dao.SuratDaftar.Ctx(ctx).
		OrderAsc("nomor").
		Page(req.Page, req.Size).
		Scan(&suratList)
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节列表失败")
	}

	// 转换数据
	var list []*v1.QuranSuratItem
	for _, surat := range suratList {
		list = append(list, &v1.QuranSuratItem{
			Id:          surat.Id,
			Nomor:       surat.Nomor,
			Nama:        surat.Nama,
			NamaLatin:   surat.NamaLatin,
			JumlahAyat:  surat.JumlahAyat,
			TempatTurun: surat.TempatTurun,
			Arti:        surat.Arti,
			Audio:       surat.Audio,
			Status:      surat.Status,
		})
	}

	res = &v1.QuranCrawlerSuratListRes{
		List:  list,
		Total: int(total),
		Page:  req.Page,
		Size:  req.Size,
	}

	return res, nil
}

// SuratDetail 获取章节详情
func (s *sQuranCrawler) SuratDetail(ctx context.Context, req *v1.QuranCrawlerSuratDetailReq) (res *v1.QuranCrawlerSuratDetailRes, err error) {
	// 获取章节信息
	var surat islamic_entity.SuratDaftar
	err = islamic_dao.SuratDaftar.Ctx(ctx).Where("nomor", req.Nomor).Scan(&surat)
	if err != nil {
		return nil, gerror.Wrap(err, "获取章节信息失败")
	}
	if surat.Id == 0 {
		return nil, gerror.New("章节不存在")
	}

	// 获取经文列表
	var ayatList []islamic_entity.SuratAyat
	err = islamic_dao.SuratAyat.Ctx(ctx).Where("surah_id", req.Nomor).OrderAsc("nomor").Scan(&ayatList)
	if err != nil {
		return nil, gerror.Wrap(err, "获取经文列表失败")
	}

	// 获取注释列表
	var tafsirList []islamic_entity.SuratTafsir
	err = islamic_dao.SuratTafsir.Ctx(ctx).Where("surah_id", req.Nomor).OrderAsc("ayat_nomor").Scan(&tafsirList)
	if err != nil {
		return nil, gerror.Wrap(err, "获取注释列表失败")
	}

	// 转换数据
	var ayat []*v1.QuranAyat
	for _, a := range ayatList {
		ayat = append(ayat, &v1.QuranAyat{
			Id:      a.Id,
			AyatId:  a.AyatId,
			SurahId: a.SurahId,
			Nomor:   a.Nomor,
			Ar:      a.Ar,
			Tr:      a.Tr,
			Idn:     a.Idn,
		})
	}

	var tafsir []*v1.QuranTafsir
	for _, t := range tafsirList {
		tafsir = append(tafsir, &v1.QuranTafsir{
			Id:        t.Id,
			TafsirId:  t.TafsirId,
			SurahId:   t.SurahId,
			AyatNomor: t.AyatNomor,
			Tafsir:    t.Tafsir,
		})
	}

	res = &v1.QuranCrawlerSuratDetailRes{
		Surat: &v1.QuranSuratItem{
			Id:          surat.Id,
			Nomor:       surat.Nomor,
			Nama:        surat.Nama,
			NamaLatin:   surat.NamaLatin,
			JumlahAyat:  surat.JumlahAyat,
			TempatTurun: surat.TempatTurun,
			Arti:        surat.Arti,
			Audio:       surat.Audio,
			Status:      surat.Status,
		},
		Ayat:   ayat,
		Tafsir: tafsir,
	}

	return res, nil
}
