// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectKogPlayerDao is the data access object for the table collect_kog_player.
// You can define custom methods on it to extend its functionality as needed.
type collectKogPlayerDao struct {
	*internal.CollectKogPlayerDao
}

var (
	// CollectKogPlayer is a globally accessible object for table collect_kog_player operations.
	CollectKogPlayer = collectKogPlayerDao{internal.NewCollectKogPlayerDao()}
)

// Add your custom methods and functionality below.
