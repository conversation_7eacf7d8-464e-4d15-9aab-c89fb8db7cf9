// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectDotaTeamDao is the data access object for the table collect_dota_team.
// You can define custom methods on it to extend its functionality as needed.
type collectDotaTeamDao struct {
	*internal.CollectDotaTeamDao
}

var (
	// CollectDotaTeam is a globally accessible object for table collect_dota_team operations.
	CollectDotaTeam = collectDotaTeamDao{internal.NewCollectDotaTeamDao()}
)

// Add your custom methods and functionality below.
