// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectDotaPlayerDao is the data access object for the table collect_dota_player.
// You can define custom methods on it to extend its functionality as needed.
type collectDotaPlayerDao struct {
	*internal.CollectDotaPlayerDao
}

var (
	// CollectDotaPlayer is a globally accessible object for table collect_dota_player operations.
	CollectDotaPlayer = collectDotaPlayerDao{internal.NewCollectDotaPlayerDao()}
)

// Add your custom methods and functionality below.
