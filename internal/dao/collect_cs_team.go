// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectCsTeamDao is the data access object for the table collect_cs_team.
// You can define custom methods on it to extend its functionality as needed.
type collectCsTeamDao struct {
	*internal.CollectCsTeamDao
}

var (
	// CollectCsTeam is a globally accessible object for table collect_cs_team operations.
	CollectCsTeam = collectCsTeamDao{internal.NewCollectCsTeamDao()}
)

// Add your custom methods and functionality below.
