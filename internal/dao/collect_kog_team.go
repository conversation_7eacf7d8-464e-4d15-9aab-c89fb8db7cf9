// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectKogTeamDao is the data access object for the table collect_kog_team.
// You can define custom methods on it to extend its functionality as needed.
type collectKogTeamDao struct {
	*internal.CollectKogTeamDao
}

var (
	// CollectKogTeam is a globally accessible object for table collect_kog_team operations.
	CollectKogTeam = collectKogTeamDao{internal.NewCollectKogTeamDao()}
)

// Add your custom methods and functionality below.
