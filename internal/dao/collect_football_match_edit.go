// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectFootballMatchEditDao is the data access object for the table collect_football_match_edit.
// You can define custom methods on it to extend its functionality as needed.
type collectFootballMatchEditDao struct {
	*internal.CollectFootballMatchEditDao
}

var (
	// CollectFootballMatchEdit is a globally accessible object for table collect_football_match_edit operations.
	CollectFootballMatchEdit = collectFootballMatchEditDao{internal.NewCollectFootballMatchEditDao()}
)

// Add your custom methods and functionality below.
