// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// cryptoRatesLoosersDao is the data access object for the table crypto_rates_loosers.
// You can define custom methods on it to extend its functionality as needed.
type cryptoRatesLoosersDao struct {
	*internal.CryptoRatesLoosersDao
}

var (
	// CryptoRatesLoosers is a globally accessible object for table crypto_rates_loosers operations.
	CryptoRatesLoosers = cryptoRatesLoosersDao{internal.NewCryptoRatesLoosersDao()}
)

// Add your custom methods and functionality below.
