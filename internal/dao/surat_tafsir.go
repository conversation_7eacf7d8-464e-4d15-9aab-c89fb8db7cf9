// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// suratTafsirDao is the data access object for the table surat_tafsir.
// You can define custom methods on it to extend its functionality as needed.
type suratTafsirDao struct {
	*internal.SuratTafsirDao
}

var (
	// SuratTafsir is a globally accessible object for table surat_tafsir operations.
	SuratTafsir = suratTafsirDao{internal.NewSuratTafsirDao()}
)

// Add your custom methods and functionality below.
