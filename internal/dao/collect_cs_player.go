// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectCsPlayerDao is the data access object for the table collect_cs_player.
// You can define custom methods on it to extend its functionality as needed.
type collectCsPlayerDao struct {
	*internal.CollectCsPlayerDao
}

var (
	// CollectCsPlayer is a globally accessible object for table collect_cs_player operations.
	CollectCsPlayer = collectCsPlayerDao{internal.NewCollectCsPlayerDao()}
)

// Add your custom methods and functionality below.
