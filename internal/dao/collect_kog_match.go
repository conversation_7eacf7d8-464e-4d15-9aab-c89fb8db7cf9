// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectKogMatchDao is the data access object for the table collect_kog_match.
// You can define custom methods on it to extend its functionality as needed.
type collectKogMatchDao struct {
	*internal.CollectKogMatchDao
}

var (
	// CollectKogMatch is a globally accessible object for table collect_kog_match operations.
	CollectKogMatch = collectKogMatchDao{internal.NewCollectKogMatchDao()}
)

// Add your custom methods and functionality below.
