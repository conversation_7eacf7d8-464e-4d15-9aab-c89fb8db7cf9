// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CryptoRatesHeaderDao is the data access object for the table crypto_rates_header.
type CryptoRatesHeaderDao struct {
	table    string                   // table is the underlying table name of the DAO.
	group    string                   // group is the database configuration group name of the current DAO.
	columns  CryptoRatesHeaderColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler       // handlers for customized model modification.
}

// CryptoRatesHeaderColumns defines and stores column names for the table crypto_rates_header.
type CryptoRatesHeaderColumns struct {
	Rank         string //
	CryptoSymbol string //
	Name         string //
	Slug         string //
	Price        string //
	Change24H    string //
	Change1H     string //
	Change7D     string //
	MarketCap    string //
	Volume24H    string //
	IsTrending   string //
	CreateTime   string //
	Image        string //
}

// cryptoRatesHeaderColumns holds the columns for the table crypto_rates_header.
var cryptoRatesHeaderColumns = CryptoRatesHeaderColumns{
	Rank:         "rank",
	CryptoSymbol: "crypto_symbol",
	Name:         "name",
	Slug:         "slug",
	Price:        "price",
	Change24H:    "change_24h",
	Change1H:     "change_1h",
	Change7D:     "change_7d",
	MarketCap:    "market_cap",
	Volume24H:    "volume_24h",
	IsTrending:   "is_trending",
	CreateTime:   "create_time",
	Image:        "image",
}

// NewCryptoRatesHeaderDao creates and returns a new DAO object for table data access.
func NewCryptoRatesHeaderDao(handlers ...gdb.ModelHandler) *CryptoRatesHeaderDao {
	return &CryptoRatesHeaderDao{
		group:    "default",
		table:    "crypto_rates_header",
		columns:  cryptoRatesHeaderColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CryptoRatesHeaderDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CryptoRatesHeaderDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CryptoRatesHeaderDao) Columns() CryptoRatesHeaderColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CryptoRatesHeaderDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CryptoRatesHeaderDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CryptoRatesHeaderDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
