// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SeoIndexTitleDao is the data access object for the table seo_index_title.
type SeoIndexTitleDao struct {
	table    string               // table is the underlying table name of the DAO.
	group    string               // group is the database configuration group name of the current DAO.
	columns  SeoIndexTitleColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler   // handlers for customized model modification.
}

// SeoIndexTitleColumns defines and stores column names for the table seo_index_title.
type SeoIndexTitleColumns struct {
	Id        string // id
	Url       string // url
	Title     string // title
	Link      string // link
	UpdatedAt string // 更新时间
	CreatedAt string // 插入时间
	Creater   string // 创建者id
}

// seoIndexTitleColumns holds the columns for the table seo_index_title.
var seoIndexTitleColumns = SeoIndexTitleColumns{
	Id:        "id",
	Url:       "url",
	Title:     "title",
	Link:      "link",
	UpdatedAt: "updated_at",
	CreatedAt: "created_at",
	Creater:   "creater",
}

// NewSeoIndexTitleDao creates and returns a new DAO object for table data access.
func NewSeoIndexTitleDao(handlers ...gdb.ModelHandler) *SeoIndexTitleDao {
	return &SeoIndexTitleDao{
		group:    "default",
		table:    "seo_index_title",
		columns:  seoIndexTitleColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SeoIndexTitleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SeoIndexTitleDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SeoIndexTitleDao) Columns() SeoIndexTitleColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SeoIndexTitleDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SeoIndexTitleDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SeoIndexTitleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
