// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// RouterDao is the data access object for the table router.
type RouterDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  RouterColumns      // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// RouterColumns defines and stores column names for the table router.
type RouterColumns struct {
	Id         string //
	SiteId     string // 站点id(关联site表)
	Url        string // url
	RelationId string // 关联id(关联collect_xxx_competition表/label表等)
}

// routerColumns holds the columns for the table router.
var routerColumns = RouterColumns{
	Id:         "id",
	SiteId:     "site_id",
	Url:        "url",
	RelationId: "relation_id",
}

// NewRouterDao creates and returns a new DAO object for table data access.
func NewRouterDao(handlers ...gdb.ModelHandler) *RouterDao {
	return &RouterDao{
		group:    "default",
		table:    "router",
		columns:  routerColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *RouterDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *RouterDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *RouterDao) Columns() RouterColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *RouterDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *RouterDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *RouterDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
