// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectFootballMatchEditDao is the data access object for the table collect_football_match_edit.
type CollectFootballMatchEditDao struct {
	table    string                          // table is the underlying table name of the DAO.
	group    string                          // group is the database configuration group name of the current DAO.
	columns  CollectFootballMatchEditColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler              // handlers for customized model modification.
}

// CollectFootballMatchEditColumns defines and stores column names for the table collect_football_match_edit.
type CollectFootballMatchEditColumns struct {
	Id         string //
	MatchId    string //
	HomeScores string // 主队比分
	AwayScores string // 客队比分
}

// collectFootballMatchEditColumns holds the columns for the table collect_football_match_edit.
var collectFootballMatchEditColumns = CollectFootballMatchEditColumns{
	Id:         "id",
	MatchId:    "match_id",
	HomeScores: "home_scores",
	AwayScores: "away_scores",
}

// NewCollectFootballMatchEditDao creates and returns a new DAO object for table data access.
func NewCollectFootballMatchEditDao(handlers ...gdb.ModelHandler) *CollectFootballMatchEditDao {
	return &CollectFootballMatchEditDao{
		group:    "default",
		table:    "collect_football_match_edit",
		columns:  collectFootballMatchEditColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectFootballMatchEditDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectFootballMatchEditDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectFootballMatchEditDao) Columns() CollectFootballMatchEditColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectFootballMatchEditDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectFootballMatchEditDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectFootballMatchEditDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
