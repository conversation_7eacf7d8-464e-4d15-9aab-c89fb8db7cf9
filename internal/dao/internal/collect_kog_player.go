// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectKogPlayerDao is the data access object for the table collect_kog_player.
type CollectKogPlayerDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  CollectKogPlayerColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// CollectKogPlayerColumns defines and stores column names for the table collect_kog_player.
type CollectKogPlayerColumns struct {
	Id        string // 选手ID
	NameZh    string // 中文名称
	NameEn    string // 英文名称
	AbbrZh    string // 中文简称
	AbbrEn    string // 英文简称
	Logo      string // 选手Logo
	TeamId    string // 所属战队ID
	CountryId string // 所属国家ID
	RealName  string // 真实名称
	Birthday  string // 生日（时间戳）
	Retired   string // 是否退役（1-是，0-否）
	Status    string // 选手状态（1-首发，2-替补）
	Position  string // 位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）
	UpdatedAt string // 更新时间（时间戳）
}

// collectKogPlayerColumns holds the columns for the table collect_kog_player.
var collectKogPlayerColumns = CollectKogPlayerColumns{
	Id:        "id",
	NameZh:    "name_zh",
	NameEn:    "name_en",
	AbbrZh:    "abbr_zh",
	AbbrEn:    "abbr_en",
	Logo:      "logo",
	TeamId:    "team_id",
	CountryId: "country_id",
	RealName:  "real_name",
	Birthday:  "birthday",
	Retired:   "retired",
	Status:    "status",
	Position:  "position",
	UpdatedAt: "updated_at",
}

// NewCollectKogPlayerDao creates and returns a new DAO object for table data access.
func NewCollectKogPlayerDao(handlers ...gdb.ModelHandler) *CollectKogPlayerDao {
	return &CollectKogPlayerDao{
		group:    "default",
		table:    "collect_kog_player",
		columns:  collectKogPlayerColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectKogPlayerDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectKogPlayerDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectKogPlayerDao) Columns() CollectKogPlayerColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectKogPlayerDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectKogPlayerDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectKogPlayerDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
