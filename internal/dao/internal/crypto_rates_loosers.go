// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CryptoRatesLoosersDao is the data access object for the table crypto_rates_loosers.
type CryptoRatesLoosersDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  CryptoRatesLoosersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// CryptoRatesLoosersColumns defines and stores column names for the table crypto_rates_loosers.
type CryptoRatesLoosersColumns struct {
	Rank         string //
	CryptoSymbol string //
	Name         string //
	Slug         string //
	Price        string //
	Change24H    string //
	Change1H     string //
	Change7D     string //
	MarketCap    string //
	Volume24H    string //
	IsTrending   string //
	CreateTime   string //
	Image        string //
}

// cryptoRatesLoosersColumns holds the columns for the table crypto_rates_loosers.
var cryptoRatesLoosersColumns = CryptoRatesLoosersColumns{
	Rank:         "rank",
	CryptoSymbol: "crypto_symbol",
	Name:         "name",
	Slug:         "slug",
	Price:        "price",
	Change24H:    "change_24h",
	Change1H:     "change_1h",
	Change7D:     "change_7d",
	MarketCap:    "market_cap",
	Volume24H:    "volume_24h",
	IsTrending:   "is_trending",
	CreateTime:   "create_time",
	Image:        "image",
}

// NewCryptoRatesLoosersDao creates and returns a new DAO object for table data access.
func NewCryptoRatesLoosersDao(handlers ...gdb.ModelHandler) *CryptoRatesLoosersDao {
	return &CryptoRatesLoosersDao{
		group:    "default",
		table:    "crypto_rates_loosers",
		columns:  cryptoRatesLoosersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CryptoRatesLoosersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CryptoRatesLoosersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CryptoRatesLoosersDao) Columns() CryptoRatesLoosersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CryptoRatesLoosersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CryptoRatesLoosersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CryptoRatesLoosersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
