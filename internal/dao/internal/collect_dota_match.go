// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectDotaMatchDao is the data access object for the table collect_dota_match.
type CollectDotaMatchDao struct {
	table    string                  // table is the underlying table name of the DAO.
	group    string                  // group is the database configuration group name of the current DAO.
	columns  CollectDotaMatchColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler      // handlers for customized model modification.
}

// CollectDotaMatchColumns defines and stores column names for the table collect_dota_match.
type CollectDotaMatchColumns struct {
	Id            string // 比赛ID
	Box           string // 总局数（BO1、BO3等）
	TournamentId  string // 赛事ID
	StageId       string // 阶段ID
	HomeTeamId    string // 主队战队ID
	HomeScore     string // 主队获胜局数
	AwayTeamId    string // 客队战队ID
	AwayScore     string // 客队获胜局数
	StatusId      string // 比赛状态（详见状态码）
	MatchTime     string // 比赛时间（时间戳）
	Mlive         string // 是否有动画（1是，0否）
	Description   string // 比赛说明/备注
	UpdatedAt     string // 更新时间（时间戳）
	MatchTs       string // 比赛时间的时间戳
	CompetitionId string // 赛事id
}

// collectDotaMatchColumns holds the columns for the table collect_dota_match.
var collectDotaMatchColumns = CollectDotaMatchColumns{
	Id:            "id",
	Box:           "box",
	TournamentId:  "tournament_id",
	StageId:       "stage_id",
	HomeTeamId:    "home_team_id",
	HomeScore:     "home_score",
	AwayTeamId:    "away_team_id",
	AwayScore:     "away_score",
	StatusId:      "status_id",
	MatchTime:     "match_time",
	Mlive:         "mlive",
	Description:   "description",
	UpdatedAt:     "updated_at",
	MatchTs:       "match_ts",
	CompetitionId: "competition_id",
}

// NewCollectDotaMatchDao creates and returns a new DAO object for table data access.
func NewCollectDotaMatchDao(handlers ...gdb.ModelHandler) *CollectDotaMatchDao {
	return &CollectDotaMatchDao{
		group:    "default",
		table:    "collect_dota_match",
		columns:  collectDotaMatchColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectDotaMatchDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectDotaMatchDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectDotaMatchDao) Columns() CollectDotaMatchColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectDotaMatchDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectDotaMatchDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectDotaMatchDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
