// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectDotaCompetitionDao is the data access object for the table collect_dota_competition.
type CollectDotaCompetitionDao struct {
	table    string                        // table is the underlying table name of the DAO.
	group    string                        // group is the database configuration group name of the current DAO.
	columns  CollectDotaCompetitionColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler            // handlers for customized model modification.
}

// CollectDotaCompetitionColumns defines and stores column names for the table collect_dota_competition.
type CollectDotaCompetitionColumns struct {
	Id         string // 赛事ID
	NameZh     string // 中文名称
	NameEn     string // 英文名称
	AbbrZh     string // 中文简称
	AbbrEn     string // 英文简称
	StatusId   string // 状态，详见状态码
	Logo       string // 赛事logo
	Cover      string // 封面
	StartTime  string // 开始时间（时间戳）
	EndTime    string // 结束时间（时间戳）
	Type       string // 赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）
	CityName   string // 举办地 中文名称
	CityNameEn string // 举办地 英文名称
	PricePool  string // 奖金池
	UpdatedAt  string // 更新时间（时间戳）
}

// collectDotaCompetitionColumns holds the columns for the table collect_dota_competition.
var collectDotaCompetitionColumns = CollectDotaCompetitionColumns{
	Id:         "id",
	NameZh:     "name_zh",
	NameEn:     "name_en",
	AbbrZh:     "abbr_zh",
	AbbrEn:     "abbr_en",
	StatusId:   "status_id",
	Logo:       "logo",
	Cover:      "cover",
	StartTime:  "start_time",
	EndTime:    "end_time",
	Type:       "type",
	CityName:   "city_name",
	CityNameEn: "city_name_en",
	PricePool:  "price_pool",
	UpdatedAt:  "updated_at",
}

// NewCollectDotaCompetitionDao creates and returns a new DAO object for table data access.
func NewCollectDotaCompetitionDao(handlers ...gdb.ModelHandler) *CollectDotaCompetitionDao {
	return &CollectDotaCompetitionDao{
		group:    "default",
		table:    "collect_dota_competition",
		columns:  collectDotaCompetitionColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectDotaCompetitionDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectDotaCompetitionDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectDotaCompetitionDao) Columns() CollectDotaCompetitionColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectDotaCompetitionDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectDotaCompetitionDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectDotaCompetitionDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
