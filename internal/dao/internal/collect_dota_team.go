// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CollectDotaTeamDao is the data access object for the table collect_dota_team.
type CollectDotaTeamDao struct {
	table    string                 // table is the underlying table name of the DAO.
	group    string                 // group is the database configuration group name of the current DAO.
	columns  CollectDotaTeamColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler     // handlers for customized model modification.
}

// CollectDotaTeamColumns defines and stores column names for the table collect_dota_team.
type CollectDotaTeamColumns struct {
	Id            string // 战队ID
	NameZh        string // 中文名称
	NameEn        string // 英文名称
	AbbrZh        string // 中文简称
	AbbrEn        string // 英文简称
	Logo          string // 战队Logo
	CountryId     string // 所属国家ID
	RegionId      string // 所属赛区ID
	CreateTime    string // 战队成立时间（时间戳）
	TotalEarnings string // 总奖金（字符串）
	UpdatedAt     string // 更新时间（时间戳）
}

// collectDotaTeamColumns holds the columns for the table collect_dota_team.
var collectDotaTeamColumns = CollectDotaTeamColumns{
	Id:            "id",
	NameZh:        "name_zh",
	NameEn:        "name_en",
	AbbrZh:        "abbr_zh",
	AbbrEn:        "abbr_en",
	Logo:          "logo",
	CountryId:     "country_id",
	RegionId:      "region_id",
	CreateTime:    "create_time",
	TotalEarnings: "total_earnings",
	UpdatedAt:     "updated_at",
}

// NewCollectDotaTeamDao creates and returns a new DAO object for table data access.
func NewCollectDotaTeamDao(handlers ...gdb.ModelHandler) *CollectDotaTeamDao {
	return &CollectDotaTeamDao{
		group:    "default",
		table:    "collect_dota_team",
		columns:  collectDotaTeamColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CollectDotaTeamDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CollectDotaTeamDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CollectDotaTeamDao) Columns() CollectDotaTeamColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CollectDotaTeamDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CollectDotaTeamDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CollectDotaTeamDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
