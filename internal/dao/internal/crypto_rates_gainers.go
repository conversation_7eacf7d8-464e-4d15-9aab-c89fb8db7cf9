// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// CryptoRatesGainersDao is the data access object for the table crypto_rates_gainers.
type CryptoRatesGainersDao struct {
	table    string                    // table is the underlying table name of the DAO.
	group    string                    // group is the database configuration group name of the current DAO.
	columns  CryptoRatesGainersColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler        // handlers for customized model modification.
}

// CryptoRatesGainersColumns defines and stores column names for the table crypto_rates_gainers.
type CryptoRatesGainersColumns struct {
	Rank         string //
	CryptoSymbol string //
	Name         string //
	Slug         string //
	Price        string //
	Change24H    string //
	Change1H     string //
	Change7D     string //
	MarketCap    string //
	Volume24H    string //
	IsTrending   string //
	CreateTime   string //
	Image        string //
}

// cryptoRatesGainersColumns holds the columns for the table crypto_rates_gainers.
var cryptoRatesGainersColumns = CryptoRatesGainersColumns{
	Rank:         "rank",
	CryptoSymbol: "crypto_symbol",
	Name:         "name",
	Slug:         "slug",
	Price:        "price",
	Change24H:    "change_24h",
	Change1H:     "change_1h",
	Change7D:     "change_7d",
	MarketCap:    "market_cap",
	Volume24H:    "volume_24h",
	IsTrending:   "is_trending",
	CreateTime:   "create_time",
	Image:        "image",
}

// NewCryptoRatesGainersDao creates and returns a new DAO object for table data access.
func NewCryptoRatesGainersDao(handlers ...gdb.ModelHandler) *CryptoRatesGainersDao {
	return &CryptoRatesGainersDao{
		group:    "default",
		table:    "crypto_rates_gainers",
		columns:  cryptoRatesGainersColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *CryptoRatesGainersDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *CryptoRatesGainersDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *CryptoRatesGainersDao) Columns() CryptoRatesGainersColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *CryptoRatesGainersDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *CryptoRatesGainersDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *CryptoRatesGainersDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
