// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SeoIndexDao is the data access object for the table seo_index.
type SeoIndexDao struct {
	table    string             // table is the underlying table name of the DAO.
	group    string             // group is the database configuration group name of the current DAO.
	columns  SeoIndexColumns    // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler // handlers for customized model modification.
}

// SeoIndexColumns defines and stores column names for the table seo_index.
type SeoIndexColumns struct {
	Id        string // id
	Url       string // url
	Keywords  string // 关键词，快照时为空
	Type      string // 0-快照，1-索引
	UpdatedAt string // 更新时间
	HasIndex  string // 0-未收录，1-已收录
	HasSend   string // 0-未查询，1-已查询
	Uuid      string // uuid
	CreatedAt string // 插入时间
	Creater   string // 创建者id
	Title     string // title
}

// seoIndexColumns holds the columns for the table seo_index.
var seoIndexColumns = SeoIndexColumns{
	Id:        "id",
	Url:       "url",
	Keywords:  "keywords",
	Type:      "type",
	UpdatedAt: "updated_at",
	HasIndex:  "has_index",
	HasSend:   "has_send",
	Uuid:      "uuid",
	CreatedAt: "created_at",
	Creater:   "creater",
	Title:     "title",
}

// NewSeoIndexDao creates and returns a new DAO object for table data access.
func NewSeoIndexDao(handlers ...gdb.ModelHandler) *SeoIndexDao {
	return &SeoIndexDao{
		group:    "default",
		table:    "seo_index",
		columns:  seoIndexColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *SeoIndexDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *SeoIndexDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *SeoIndexDao) Columns() SeoIndexColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *SeoIndexDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *SeoIndexDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *SeoIndexDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
