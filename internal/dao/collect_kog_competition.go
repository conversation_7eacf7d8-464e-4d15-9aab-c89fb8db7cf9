// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectKogCompetitionDao is the data access object for the table collect_kog_competition.
// You can define custom methods on it to extend its functionality as needed.
type collectKogCompetitionDao struct {
	*internal.CollectKogCompetitionDao
}

var (
	// CollectKogCompetition is a globally accessible object for table collect_kog_competition operations.
	CollectKogCompetition = collectKogCompetitionDao{internal.NewCollectKogCompetitionDao()}
)

// Add your custom methods and functionality below.
