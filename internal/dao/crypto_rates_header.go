// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// cryptoRatesHeaderDao is the data access object for the table crypto_rates_header.
// You can define custom methods on it to extend its functionality as needed.
type cryptoRatesHeaderDao struct {
	*internal.CryptoRatesHeaderDao
}

var (
	// CryptoRatesHeader is a globally accessible object for table crypto_rates_header operations.
	CryptoRatesHeader = cryptoRatesHeaderDao{internal.NewCryptoRatesHeaderDao()}
)

// Add your custom methods and functionality below.
