// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectCsCompetitionDao is the data access object for the table collect_cs_competition.
// You can define custom methods on it to extend its functionality as needed.
type collectCsCompetitionDao struct {
	*internal.CollectCsCompetitionDao
}

var (
	// CollectCsCompetition is a globally accessible object for table collect_cs_competition operations.
	CollectCsCompetition = collectCsCompetitionDao{internal.NewCollectCsCompetitionDao()}
)

// Add your custom methods and functionality below.
