// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectCsMatchDao is the data access object for the table collect_cs_match.
// You can define custom methods on it to extend its functionality as needed.
type collectCsMatchDao struct {
	*internal.CollectCsMatchDao
}

var (
	// CollectCsMatch is a globally accessible object for table collect_cs_match operations.
	CollectCsMatch = collectCsMatchDao{internal.NewCollectCsMatchDao()}
)

// Add your custom methods and functionality below.
