// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectDotaMatchDao is the data access object for the table collect_dota_match.
// You can define custom methods on it to extend its functionality as needed.
type collectDotaMatchDao struct {
	*internal.CollectDotaMatchDao
}

var (
	// CollectDotaMatch is a globally accessible object for table collect_dota_match operations.
	CollectDotaMatch = collectDotaMatchDao{internal.NewCollectDotaMatchDao()}
)

// Add your custom methods and functionality below.
