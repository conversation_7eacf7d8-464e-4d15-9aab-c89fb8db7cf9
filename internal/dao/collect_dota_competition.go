// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// collectDotaCompetitionDao is the data access object for the table collect_dota_competition.
// You can define custom methods on it to extend its functionality as needed.
type collectDotaCompetitionDao struct {
	*internal.CollectDotaCompetitionDao
}

var (
	// CollectDotaCompetition is a globally accessible object for table collect_dota_competition operations.
	CollectDotaCompetition = collectDotaCompetitionDao{internal.NewCollectDotaCompetitionDao()}
)

// Add your custom methods and functionality below.
