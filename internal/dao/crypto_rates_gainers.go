// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// cryptoRatesGainersDao is the data access object for the table crypto_rates_gainers.
// You can define custom methods on it to extend its functionality as needed.
type cryptoRatesGainersDao struct {
	*internal.CryptoRatesGainersDao
}

var (
	// CryptoRatesGainers is a globally accessible object for table crypto_rates_gainers operations.
	CryptoRatesGainers = cryptoRatesGainersDao{internal.NewCryptoRatesGainersDao()}
)

// Add your custom methods and functionality below.
