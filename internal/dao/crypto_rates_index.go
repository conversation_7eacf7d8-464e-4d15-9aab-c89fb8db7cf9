// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// cryptoRatesIndexDao is the data access object for the table crypto_rates_index.
// You can define custom methods on it to extend its functionality as needed.
type cryptoRatesIndexDao struct {
	*internal.CryptoRatesIndexDao
}

var (
	// CryptoRatesIndex is a globally accessible object for table crypto_rates_index operations.
	CryptoRatesIndex = cryptoRatesIndexDao{internal.NewCryptoRatesIndexDao()}
)

// Add your custom methods and functionality below.
