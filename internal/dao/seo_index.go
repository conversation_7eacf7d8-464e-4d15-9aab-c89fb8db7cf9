// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// seoIndexDao is the data access object for the table seo_index.
// You can define custom methods on it to extend its functionality as needed.
type seoIndexDao struct {
	*internal.SeoIndexDao
}

var (
	// SeoIndex is a globally accessible object for table seo_index operations.
	SeoIndex = seoIndexDao{internal.NewSeoIndexDao()}
)

// Add your custom methods and functionality below.
