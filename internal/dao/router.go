// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// routerDao is the data access object for the table router.
// You can define custom methods on it to extend its functionality as needed.
type routerDao struct {
	*internal.RouterDao
}

var (
	// Router is a globally accessible object for table router operations.
	Router = routerDao{internal.NewRouterDao()}
)

// Add your custom methods and functionality below.
