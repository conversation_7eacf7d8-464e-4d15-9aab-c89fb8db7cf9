// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"gtcms/internal/dao/internal"
)

// suratAyatDao is the data access object for the table surat_ayat.
// You can define custom methods on it to extend its functionality as needed.
type suratAyatDao struct {
	*internal.SuratAyatDao
}

var (
	// SuratAyat is a globally accessible object for table surat_ayat operations.
	SuratAyat = suratAyatDao{internal.NewSuratAyatDao()}
)

// Add your custom methods and functionality below.
