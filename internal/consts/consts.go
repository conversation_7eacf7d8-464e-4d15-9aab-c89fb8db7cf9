package consts

import (
	"time"
)

const (
	JWTSecretKey       = "fFDji1e3ZV&*6BNa9#@d"
	JWTEx              = time.Hour * 24 * 365 // JWT本身的有效期
	TokenRedisEx       = 60 * 60 * 12         // 秒，token有效期
	TokenUpdateEx      = 60 * 20              // 秒，token有效期小于这个数时，刷新token有效期
	Lang               = "lang"               // 语言的key
	LangDefaultCode    = "zh-CN"              // 语言的默认编码
	OpenAPITitle       = "后台接口文档"
	OpenAPIDescription = `
	1、认证方式：在请求头加 Authorization:Bearer {{token}}；
	2、除了登录接口和获取验证码接口不需要认证，其他接口都要带认证；
	3、所有接口请求都是POST，application/json；返回都是application/json；
	4、所有接口返回格式{"code":xxx,"message":"xxx","data":{...}或[...]或null}；code:0代表业务处理成功，否则业务处理失败；失败时一般有message，成功时一般有data，也可能没有(以实际业务为主)；
	5、回调接口是供上游回调的，前端不用理会；
	6、code为1100-1119代表token无效；
	`
	CaptchaEx              = 5 * 60 // 秒
	LargeCacheEx           = time.Hour * 24 * 30
	PermissionCacheTimeout = 60

	Precision float64 = 0.0000000001 // 浮点数比较相等时的精度允许差

	OneDaySeconds      = 60 * 60 * 24        // 一天的秒值
	OneYearSeconds     = OneDaySeconds * 365 // 一年的秒数
	ContextKeyUrl      = "apiUrl"
	ContextKeyRolePerm = "role.perm"

	SportTypeFootball   = "f"
	SportTypeBasketball = "b"

	TdkTmplCategoryMatchColumn = 1 // tdk模板 赛程栏目

	IsYes = 1
	IsNo  = 2

	GoogleAuthCryptoKey = "8QrPb@6mLD#2zNv5YcXsF!3hGtR*wE6q"
	BatchSize           = 30000                      // 设置每次拉取的批次大小
	ExpireHour          = 60 * 60 * time.Second * 24 // 24小时的秒数
)
