package account

import (
	"context"
	v1 "gtcms/api/v1"
	"gtcms/internal/consts"
	"gtcms/internal/model/entity"
	"gtcms/internal/service"
	"time"
)

type Controller struct{}

func New() *Controller {
	return &Controller{}
}

func (c *Controller) AccountAdd(ctx context.Context, req *v1.AccountAddReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.Account{
		Account:  req.Account,
		Password: req.Password,
		NickName: req.NickName,
		//Contact:       req.Contact,
		Remark: req.Remark,
		RoleId: req.RoleId,
		//AuditPassword: req.AuditPassword,
		IsOnline:    2,
		IsAffect:    req.IsAffect,
		CreateTime:  time.Now().UnixMilli(),
		DeleteTime:  0,
		TemplateIds: "[]",
	}
	err = service.Account().Create(ctx, in)
	return
}
func (c *Controller) AccountDelete(ctx context.Context, req *v1.AccountDeleteReq) (res *v1.EmptyDataRes, err error) {
	err = service.Account().Delete(ctx, req.Id)
	return
}

func (c *Controller) AccountEdit(ctx context.Context, req *v1.AccountEditReq) (res *v1.EmptyDataRes, err error) {
	in := &entity.Account{
		Id:       req.Id,
		Account:  req.Account,
		NickName: req.NickName,
		//Contact:  req.Contact,
		Remark: req.Remark,
		RoleId: req.RoleId,
		//IsOnline:   2,
		IsAffect:            req.IsAffect,
		UpdateTime:          time.Now().UnixMilli(),
		DeleteTime:          0,
		IsRequireGoogleAuth: req.IsRequireGoogleAuth,
	}
	err = service.Account().Edit(ctx, req.Password, req.Password, in)
	return
}
func (c *Controller) AccountDetail(ctx context.Context, req *v1.AccountDetailReq) (res *v1.AccountDetailRes, err error) {
	res, err = service.Account().Detail(ctx, req.Id)
	return
}

func (c *Controller) AccountSetStatus(ctx context.Context, req *v1.AccountSetStatusReq) (res *v1.EmptyDataRes, err error) {
	err = service.Account().SetStatus(ctx, req.Id, "", req.IsAffect)
	return
}

func (c *Controller) AccountList(ctx context.Context, req *v1.AccountListReq) (res *v1.AccountListRes, err error) {
	res, err = service.Account().List(ctx, req)
	return
}

func (c *Controller) AccountName(ctx context.Context, req *v1.AccountNameReq) (res *v1.AccountNameRes, err error) {
	res = &v1.AccountNameRes{}
	res.MapIdName, err = service.Account().MapIDName(ctx)
	return
}

func (c *Controller) AccountSetGoogleAuth(ctx context.Context, req *v1.AccountSetGoogleAuthReq) (res *v1.EmptyDataRes, err error) {
	err = service.Account().SetGoogleAuth(ctx, req.Id, "", req.IsAffect)
	return
}

func (c *Controller) AccountDetection(ctx context.Context, req *v1.AccountDetectionReq) (res *v1.AccountDetectionRes, err error) {
	res = &v1.AccountDetectionRes{
		IsExistGoogleOtp: false,
	}
	var out *entity.Account
	out, err = service.Account().GetByAccount(ctx, req.Account)
	if err != nil {
		return
	}

	if out.IsRequireGoogleAuth == consts.IsYes {
		res.IsExistGoogleOtp = true
	}

	return
}

func (c *Controller) AccountSetTmpl(ctx context.Context, req *v1.AccountSetTemplateReq) (res *v1.EmptyDataRes, err error) {
	return service.Account().SetTmpl(ctx, req)
}
