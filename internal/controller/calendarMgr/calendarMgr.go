package calendarMgr

import (
	"context"

	v1 "gtcms/api/v1"
	"gtcms/internal/service"
)

// Controller 日历管理控制器
type Controller struct{}

// New 创建日历管理控制器
func New() *Controller {
	return &Controller{}
}

// Init 初始化日历数据
func (c *Controller) Init(ctx context.Context, req *v1.CalendarInitReq) (res *v1.CalendarInitRes, err error) {
	err = service.CalendarMgr().Init(ctx, req.Year)
	if err != nil {
		return
	}
	res = &v1.CalendarInitRes{
		Success: true,
		Message: "初始化成功",
	}
	return
}
