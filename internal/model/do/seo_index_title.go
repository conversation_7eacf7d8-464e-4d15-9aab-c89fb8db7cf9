// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SeoIndexTitle is the golang structure of table seo_index_title for DAO operations like Where/Data.
type SeoIndexTitle struct {
	g.Meta    `orm:"table:seo_index_title, do:true"`
	Id        interface{} // id
	Url       interface{} // url
	Title     interface{} // title
	Link      interface{} // link
	UpdatedAt interface{} // 更新时间
	CreatedAt interface{} // 插入时间
	Creater   interface{} // 创建者id
}
