// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SuratDaftar is the golang structure of table surat_daftar for DAO operations like Where/Data.
type SuratDaftar struct {
	g.Meta      `orm:"table:surat_daftar, do:true"`
	Id          interface{} //
	Nomor       interface{} // 章节编号 (1-114)
	Nama        interface{} // 阿拉伯语章节名
	NamaLatin   interface{} // 拉丁化章节名
	JumlahAyat  interface{} // 经文数量
	TempatTurun interface{} // 降示地点
	Arti        interface{} // 章节含义
	Deskripsi   interface{} // 章节描述
	Audio       interface{} // 音频文件URL
	Status      interface{} // 状态标识
	CreatedAt   *gtime.Time //
	UpdatedAt   *gtime.Time //
}
