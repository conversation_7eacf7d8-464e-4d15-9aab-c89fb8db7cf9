// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectCsTeam is the golang structure of table collect_cs_team for DAO operations like Where/Data.
type CollectCsTeam struct {
	g.Meta        `orm:"table:collect_cs_team, do:true"`
	Id            interface{} // 战队ID
	NameZh        interface{} // 中文名称
	NameEn        interface{} // 英文名称
	AbbrZh        interface{} // 中文简称
	AbbrEn        interface{} // 英文简称
	Logo          interface{} // 战队Logo
	CountryId     interface{} // 所属国家ID
	RegionId      interface{} // 所属赛区ID
	CreateTime    interface{} // 战队成立时间（时间戳）
	TotalEarnings interface{} // 总奖金（字符串）
	UpdatedAt     interface{} // 更新时间（时间戳）
}
