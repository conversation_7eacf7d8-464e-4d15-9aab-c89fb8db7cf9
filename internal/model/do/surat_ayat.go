// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// SuratAyat is the golang structure of table surat_ayat for DAO operations like Where/Data.
type SuratAyat struct {
	g.Meta    `orm:"table:surat_ayat, do:true"`
	Id        interface{} //
	AyatId    interface{} // 经文全局ID
	SurahId   interface{} // 所属章节ID
	Nomor     interface{} // 经文在章节中的编号
	Ar        interface{} // 阿拉伯语经文
	Tr        interface{} // 音译文本
	Idn       interface{} // 印尼语翻译
	CreatedAt *gtime.Time //
	UpdatedAt *gtime.Time //
}
