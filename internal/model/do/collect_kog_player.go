// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectKogPlayer is the golang structure of table collect_kog_player for DAO operations like Where/Data.
type CollectKogPlayer struct {
	g.Meta    `orm:"table:collect_kog_player, do:true"`
	Id        interface{} // 选手ID
	NameZh    interface{} // 中文名称
	NameEn    interface{} // 英文名称
	AbbrZh    interface{} // 中文简称
	AbbrEn    interface{} // 英文简称
	Logo      interface{} // 选手Logo
	TeamId    interface{} // 所属战队ID
	CountryId interface{} // 所属国家ID
	RealName  interface{} // 真实名称
	Birthday  interface{} // 生日（时间戳）
	Retired   interface{} // 是否退役（1-是，0-否）
	Status    interface{} // 选手状态（1-首发，2-替补）
	Position  interface{} // 位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）
	UpdatedAt interface{} // 更新时间（时间戳）
}
