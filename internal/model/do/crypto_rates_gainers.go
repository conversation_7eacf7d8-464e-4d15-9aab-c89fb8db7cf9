// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CryptoRatesGainers is the golang structure of table crypto_rates_gainers for DAO operations like Where/Data.
type CryptoRatesGainers struct {
	g.Meta       `orm:"table:crypto_rates_gainers, do:true"`
	Rank         interface{} //
	CryptoSymbol interface{} //
	Name         interface{} //
	Slug         interface{} //
	Price        interface{} //
	Change24H    interface{} //
	Change1H     interface{} //
	Change7D     interface{} //
	MarketCap    interface{} //
	Volume24H    interface{} //
	IsTrending   interface{} //
	CreateTime   *gtime.Time //
	Image        interface{} //
}
