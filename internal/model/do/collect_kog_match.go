// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectKogMatch is the golang structure of table collect_kog_match for DAO operations like Where/Data.
type CollectKogMatch struct {
	g.Meta        `orm:"table:collect_kog_match, do:true"`
	Id            interface{} // 比赛ID
	Box           interface{} // 总局数（BO1、BO3等）
	TournamentId  interface{} // 赛事ID
	StageId       interface{} // 阶段ID
	HomeTeamId    interface{} // 主队战队ID
	HomeScore     interface{} // 主队获胜局数
	AwayTeamId    interface{} // 客队战队ID
	AwayScore     interface{} // 客队获胜局数
	StatusId      interface{} // 比赛状态（详见状态码）
	MatchTime     interface{} // 比赛时间（时间戳）
	Mlive         interface{} // 是否有动画（1是，0否）
	Description   interface{} // 比赛说明/备注
	UpdatedAt     interface{} // 更新时间（时间戳）
	MatchTs       interface{} // 比赛时间的时间戳
	CompetitionId interface{} // 赛事id
}
