// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// SeoIndex is the golang structure of table seo_index for DAO operations like Where/Data.
type SeoIndex struct {
	g.Meta    `orm:"table:seo_index, do:true"`
	Id        interface{} // id
	Url       interface{} // url
	Keywords  interface{} // 关键词，快照时为空
	Type      interface{} // 0-快照，1-索引
	UpdatedAt interface{} // 更新时间
	HasIndex  interface{} // 0-未收录，1-已收录
	HasSend   interface{} // 0-未查询，1-已查询
	Uuid      interface{} // uuid
	CreatedAt interface{} // 插入时间
	Creater   interface{} // 创建者id
	Title     interface{} // title
}
