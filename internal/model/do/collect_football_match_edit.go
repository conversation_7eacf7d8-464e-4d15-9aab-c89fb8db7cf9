// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectFootballMatchEdit is the golang structure of table collect_football_match_edit for DAO operations like Where/Data.
type CollectFootballMatchEdit struct {
	g.Meta     `orm:"table:collect_football_match_edit, do:true"`
	Id         interface{} //
	MatchId    interface{} //
	HomeScores interface{} // 主队比分
	AwayScores interface{} // 客队比分
}
