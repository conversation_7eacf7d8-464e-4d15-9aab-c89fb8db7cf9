// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// CryptoRatesHeader is the golang structure of table crypto_rates_header for DAO operations like Where/Data.
type CryptoRatesHeader struct {
	g.Meta       `orm:"table:crypto_rates_header, do:true"`
	Rank         interface{} //
	CryptoSymbol interface{} //
	Name         interface{} //
	Slug         interface{} //
	Price        interface{} //
	Change24H    interface{} //
	Change1H     interface{} //
	Change7D     interface{} //
	MarketCap    interface{} //
	Volume24H    interface{} //
	IsTrending   interface{} //
	CreateTime   *gtime.Time //
	Image        interface{} //
}
