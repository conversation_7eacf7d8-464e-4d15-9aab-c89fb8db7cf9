// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// Router is the golang structure of table router for DAO operations like Where/Data.
type Router struct {
	g.Meta     `orm:"table:router, do:true"`
	Id         interface{} //
	SiteId     interface{} // 站点id(关联site表)
	Url        interface{} // url
	RelationId interface{} // 关联id(关联collect_xxx_competition表/label表等)
}
