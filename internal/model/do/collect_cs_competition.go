// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// CollectCsCompetition is the golang structure of table collect_cs_competition for DAO operations like Where/Data.
type CollectCsCompetition struct {
	g.Meta     `orm:"table:collect_cs_competition, do:true"`
	Id         interface{} // 赛事ID
	NameZh     interface{} // 中文名称
	NameEn     interface{} // 英文名称
	AbbrZh     interface{} // 中文简称
	AbbrEn     interface{} // 英文简称
	StatusId   interface{} // 状态，详见状态码
	Logo       interface{} // 赛事logo
	Cover      interface{} // 封面
	StartTime  interface{} // 开始时间（时间戳）
	EndTime    interface{} // 结束时间（时间戳）
	Type       interface{} // 赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）
	CityName   interface{} // 举办地 中文名称
	CityNameEn interface{} // 举办地 英文名称
	PricePool  interface{} // 奖金池
	UpdatedAt  interface{} // 更新时间（时间戳）
}
