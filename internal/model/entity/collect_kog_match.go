// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectKogMatch is the golang structure for table collect_kog_match.
type CollectKogMatch struct {
	Id            int    `json:"id"            orm:"id"             description:"比赛ID"`
	Box           int    `json:"box"           orm:"box"            description:"总局数（BO1、BO3等）"`
	TournamentId  int    `json:"tournamentId"  orm:"tournament_id"  description:"赛事ID"`
	StageId       int    `json:"stageId"       orm:"stage_id"       description:"阶段ID"`
	HomeTeamId    int    `json:"homeTeamId"    orm:"home_team_id"   description:"主队战队ID"`
	HomeScore     int    `json:"homeScore"     orm:"home_score"     description:"主队获胜局数"`
	AwayTeamId    int    `json:"awayTeamId"    orm:"away_team_id"   description:"客队战队ID"`
	AwayScore     int    `json:"awayScore"     orm:"away_score"     description:"客队获胜局数"`
	StatusId      int    `json:"statusId"      orm:"status_id"      description:"比赛状态（详见状态码）"`
	MatchTime     int    `json:"matchTime"     orm:"match_time"     description:"比赛时间（时间戳）"`
	Mlive         int    `json:"mlive"         orm:"mlive"          description:"是否有动画（1是，0否）"`
	Description   string `json:"description"   orm:"description"    description:"比赛说明/备注"`
	UpdatedAt     int    `json:"updatedAt"     orm:"updated_at"     description:"更新时间（时间戳）"`
	MatchTs       int64  `json:"matchTs"       orm:"match_ts"       description:"比赛时间的时间戳"`
	CompetitionId int    `json:"competitionId" orm:"competition_id" description:"赛事id"`
}
