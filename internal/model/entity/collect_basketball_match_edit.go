// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectBasketballMatchEdit is the golang structure for table collect_basketball_match_edit.
type CollectBasketballMatchEdit struct {
	Id         int    `json:"id"         orm:"id"          description:""`
	MatchId    int    `json:"matchId"    orm:"match_id"    description:""`
	HomeScores string `json:"homeScores" orm:"home_scores" description:"主队比分"`
	AwayScores string `json:"awayScores" orm:"away_scores" description:"客队比分"`
}
