// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectFootballMatchEdit is the golang structure for table collect_football_match_edit.
type CollectFootballMatchEdit struct {
	Id         int    `json:"id"         orm:"id"          description:""`
	MatchId    int    `json:"matchId"    orm:"match_id"    description:""`
	HomeScores string `json:"homeScores" orm:"home_scores" description:"主队比分"`
	AwayScores string `json:"awayScores" orm:"away_scores" description:"客队比分"`
}
