// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// SuratTafsir is the golang structure for table surat_tafsir.
type SuratTafsir struct {
	Id        int         `json:"id"        orm:"id"         description:""`
	TafsirId  int         `json:"tafsirId"  orm:"tafsir_id"  description:"注释全局ID"`
	SurahId   int         `json:"surahId"   orm:"surah_id"   description:"所属章节ID"`
	AyatNomor int         `json:"ayatNomor" orm:"ayat_nomor" description:"对应经文编号"`
	Tafsir    string      `json:"tafsir"    orm:"tafsir"     description:"注释内容"`
	CreatedAt *gtime.Time `json:"createdAt" orm:"created_at" description:""`
	UpdatedAt *gtime.Time `json:"updatedAt" orm:"updated_at" description:""`
}
