// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectCsCompetition is the golang structure for table collect_cs_competition.
type CollectCsCompetition struct {
	Id         int    `json:"id"         orm:"id"           description:"赛事ID"`
	NameZh     string `json:"nameZh"     orm:"name_zh"      description:"中文名称"`
	NameEn     string `json:"nameEn"     orm:"name_en"      description:"英文名称"`
	AbbrZh     string `json:"abbrZh"     orm:"abbr_zh"      description:"中文简称"`
	AbbrEn     string `json:"abbrEn"     orm:"abbr_en"      description:"英文简称"`
	StatusId   int    `json:"statusId"   orm:"status_id"    description:"状态，详见状态码"`
	Logo       string `json:"logo"       orm:"logo"         description:"赛事logo"`
	Cover      string `json:"cover"      orm:"cover"        description:"封面"`
	StartTime  int    `json:"startTime"  orm:"start_time"   description:"开始时间（时间戳）"`
	EndTime    int    `json:"endTime"    orm:"end_time"     description:"结束时间（时间戳）"`
	Type       int    `json:"type"       orm:"type"         description:"赛事类型（1.全球性、2.五大联赛、3.地区联赛、4.其他赛事、0.未知）"`
	CityName   string `json:"cityName"   orm:"city_name"    description:"举办地 中文名称"`
	CityNameEn string `json:"cityNameEn" orm:"city_name_en" description:"举办地 英文名称"`
	PricePool  string `json:"pricePool"  orm:"price_pool"   description:"奖金池"`
	UpdatedAt  int    `json:"updatedAt"  orm:"updated_at"   description:"更新时间（时间戳）"`
}
