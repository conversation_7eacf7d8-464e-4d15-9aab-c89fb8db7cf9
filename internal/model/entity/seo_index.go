// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// SeoIndex is the golang structure for table seo_index.
type SeoIndex struct {
	Id        int64  `json:"id"        orm:"id"         description:"id"`
	Url       string `json:"url"       orm:"url"        description:"url"`
	Keywords  string `json:"keywords"  orm:"keywords"   description:"关键词，快照时为空"`
	Type      int    `json:"type"      orm:"type"       description:"0-快照，1-索引"`
	UpdatedAt int64  `json:"updatedAt" orm:"updated_at" description:"更新时间"`
	HasIndex  int    `json:"hasIndex"  orm:"has_index"  description:"0-未收录，1-已收录"`
	HasSend   int    `json:"hasSend"   orm:"has_send"   description:"0-未查询，1-已查询"`
	Uuid      string `json:"uuid"      orm:"uuid"       description:"uuid"`
	CreatedAt int64  `json:"createdAt" orm:"created_at" description:"插入时间"`
	Creater   uint   `json:"creater"   orm:"creater"    description:"创建者id"`
	Title     string `json:"title"     orm:"title"      description:"title"`
}
