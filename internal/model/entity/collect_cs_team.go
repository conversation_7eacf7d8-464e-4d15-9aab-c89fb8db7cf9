// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectCsTeam is the golang structure for table collect_cs_team.
type CollectCsTeam struct {
	Id            int    `json:"id"            orm:"id"             description:"战队ID"`
	NameZh        string `json:"nameZh"        orm:"name_zh"        description:"中文名称"`
	NameEn        string `json:"nameEn"        orm:"name_en"        description:"英文名称"`
	AbbrZh        string `json:"abbrZh"        orm:"abbr_zh"        description:"中文简称"`
	AbbrEn        string `json:"abbrEn"        orm:"abbr_en"        description:"英文简称"`
	Logo          string `json:"logo"          orm:"logo"           description:"战队Logo"`
	CountryId     int    `json:"countryId"     orm:"country_id"     description:"所属国家ID"`
	RegionId      int    `json:"regionId"      orm:"region_id"      description:"所属赛区ID"`
	CreateTime    int    `json:"createTime"    orm:"create_time"    description:"战队成立时间（时间戳）"`
	TotalEarnings string `json:"totalEarnings" orm:"total_earnings" description:"总奖金（字符串）"`
	UpdatedAt     int    `json:"updatedAt"     orm:"updated_at"     description:"更新时间（时间戳）"`
}
