// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// CollectKogPlayer is the golang structure for table collect_kog_player.
type CollectKogPlayer struct {
	Id        int    `json:"id"        orm:"id"         description:"选手ID"`
	NameZh    string `json:"nameZh"    orm:"name_zh"    description:"中文名称"`
	NameEn    string `json:"nameEn"    orm:"name_en"    description:"英文名称"`
	AbbrZh    string `json:"abbrZh"    orm:"abbr_zh"    description:"中文简称"`
	AbbrEn    string `json:"abbrEn"    orm:"abbr_en"    description:"英文简称"`
	Logo      string `json:"logo"      orm:"logo"       description:"选手Logo"`
	TeamId    int    `json:"teamId"    orm:"team_id"    description:"所属战队ID"`
	CountryId int    `json:"countryId" orm:"country_id" description:"所属国家ID"`
	RealName  string `json:"realName"  orm:"real_name"  description:"真实名称"`
	Birthday  int    `json:"birthday"  orm:"birthday"   description:"生日（时间戳）"`
	Retired   int    `json:"retired"   orm:"retired"    description:"是否退役（1-是，0-否）"`
	Status    int    `json:"status"    orm:"status"     description:"选手状态（1-首发，2-替补）"`
	Position  int    `json:"position"  orm:"position"   description:"位置（1-adc，2-中单，3-上单，4-打野，5-辅助，0-未知）"`
	UpdatedAt int    `json:"updatedAt" orm:"updated_at" description:"更新时间（时间戳）"`
}
