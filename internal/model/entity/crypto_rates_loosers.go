// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// CryptoRatesLoosers is the golang structure for table crypto_rates_loosers.
type CryptoRatesLoosers struct {
	Rank         int             `json:"rank"         orm:"rank"          description:""`
	CryptoSymbol string          `json:"cryptoSymbol" orm:"crypto_symbol" description:""`
	Name         string          `json:"name"         orm:"name"          description:""`
	Slug         string          `json:"slug"         orm:"slug"          description:""`
	Price        decimal.Decimal `json:"price"        orm:"price"         description:""`
	Change24H    decimal.Decimal `json:"change24H"    orm:"change_24h"    description:""`
	Change1H     decimal.Decimal `json:"change1H"     orm:"change_1h"     description:""`
	Change7D     decimal.Decimal `json:"change7D"     orm:"change_7d"     description:""`
	MarketCap    decimal.Decimal `json:"marketCap"    orm:"market_cap"    description:""`
	Volume24H    decimal.Decimal `json:"volume24H"    orm:"volume_24h"    description:""`
	IsTrending   int             `json:"isTrending"   orm:"is_trending"   description:""`
	CreateTime   *gtime.Time     `json:"createTime"   orm:"create_time"   description:""`
	Image        string          `json:"image"        orm:"image"         description:""`
}
